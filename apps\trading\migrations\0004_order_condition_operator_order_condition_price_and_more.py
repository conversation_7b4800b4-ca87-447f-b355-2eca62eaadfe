# Generated by Django 5.2.3 on 2025-07-03 13:03

import django.db.models.deletion
import uuid
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("trading", "0003_order_trade_order_trading_ord_account_6a1893_idx_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="order",
            name="condition_operator",
            field=models.CharField(
                blank=True,
                choices=[
                    ("gte", "Greater Than or Equal"),
                    ("lte", "Less Than or Equal"),
                    ("eq", "Equal To"),
                ],
                help_text="Condition operator for conditional orders",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="condition_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=4,
                help_text="Trigger price for conditional orders",
                max_digits=12,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="condition_symbol",
            field=models.<PERSON><PERSON><PERSON><PERSON>(
                blank=True,
                help_text="Symbol to watch for conditional orders",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="highest_price",
            field=models.Decimal<PERSON>ield(
                blank=True,
                decimal_places=4,
                help_text="Highest price reached (for trailing stops)",
                max_digits=12,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="lowest_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=4,
                help_text="Lowest price reached (for trailing stops)",
                max_digits=12,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="parent_order",
            field=models.ForeignKey(
                blank=True,
                help_text="Parent order for bracket/OCO orders",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="child_orders",
                to="trading.order",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="trailing_amount",
            field=models.DecimalField(
                blank=True,
                decimal_places=4,
                help_text="Trailing amount for trailing stop orders",
                max_digits=12,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="trailing_percent",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Trailing percentage for trailing stop orders",
                max_digits=5,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="order",
            name="order_type",
            field=models.CharField(
                choices=[
                    ("market", "Market Order"),
                    ("limit", "Limit Order"),
                    ("stop", "Stop Order"),
                    ("stop_limit", "Stop Limit Order"),
                    ("trailing_stop", "Trailing Stop Order"),
                    ("bracket", "Bracket Order"),
                    ("conditional", "Conditional Order"),
                ],
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="RiskManagement",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "max_position_size_percent",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("10.00"),
                        help_text="Maximum position size as percentage of portfolio",
                        max_digits=5,
                    ),
                ),
                (
                    "max_position_size_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum position size in dollar amount",
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "max_daily_loss_percent",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("5.00"),
                        help_text="Maximum daily loss as percentage of portfolio",
                        max_digits=5,
                    ),
                ),
                (
                    "max_daily_loss_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum daily loss in dollar amount",
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "max_drawdown_percent",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("20.00"),
                        help_text="Maximum drawdown as percentage of portfolio",
                        max_digits=5,
                    ),
                ),
                (
                    "min_risk_reward_ratio",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("1.50"),
                        help_text="Minimum risk/reward ratio for trades",
                        max_digits=5,
                    ),
                ),
                (
                    "max_leverage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("1.00"),
                        help_text="Maximum leverage allowed",
                        max_digits=5,
                    ),
                ),
                (
                    "margin_requirement_percent",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("50.00"),
                        help_text="Margin requirement as percentage",
                        max_digits=5,
                    ),
                ),
                ("enable_risk_alerts", models.BooleanField(default=True)),
                ("enable_drawdown_alerts", models.BooleanField(default=True)),
                ("enable_position_size_alerts", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "account",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="risk_management",
                        to="trading.account",
                    ),
                ),
            ],
            options={
                "verbose_name": "Risk Management",
                "verbose_name_plural": "Risk Management Settings",
                "db_table": "trading_risk_management",
            },
        ),
    ]
