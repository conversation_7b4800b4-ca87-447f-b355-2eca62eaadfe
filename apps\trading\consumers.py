"""
WebSocket consumers for real-time trading updates.
"""

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from decimal import Decimal
from .models import Account, Portfolio, Order, Trade, RiskManagement
from .services import TradingRiskService
from apps.market_data.models import MarketData, Symbol

logger = logging.getLogger(__name__)


class TradingConsumerMixin:
    """Base mixin for trading WebSocket consumers."""
    
    async def connect(self):
        """Handle WebSocket connection."""
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
        
        await self.accept()
        logger.info(f"WebSocket connected: {self.__class__.__name__} for user {self.scope['user'].username}")
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        logger.info(f"WebSocket disconnected: {self.__class__.__name__} with code {close_code}")
    
    async def send_json_data(self, data):
        """Send JSON data to WebSocket."""
        await self.send(text_data=json.dumps(data))


class PortfolioConsumer(TradingConsumerMixin, AsyncWebsocketConsumer):
    """Consumer for real-time portfolio updates."""
    
    async def connect(self):
        """Handle portfolio WebSocket connection."""
        self.account_id = self.scope['url_route']['kwargs']['account_id']
        self.group_name = f'portfolio_{self.account_id}'
        
        # Verify user has access to this account
        if not await self.verify_account_access():
            await self.close()
            return
        
        # Join portfolio group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await super().connect()
        
        # Send initial portfolio data
        await self.send_portfolio_update()
    
    async def disconnect(self, close_code):
        """Handle portfolio WebSocket disconnection."""
        # Leave portfolio group
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'request_update':
                await self.send_portfolio_update()
            elif message_type == 'ping':
                await self.send_json_data({'type': 'pong'})
                
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")
    
    async def portfolio_update(self, event):
        """Handle portfolio update from group."""
        await self.send_json_data(event['data'])
    
    @database_sync_to_async
    def verify_account_access(self):
        """Verify user has access to the account."""
        try:
            Account.objects.get(id=self.account_id, user=self.scope['user'])
            return True
        except Account.DoesNotExist:
            return False
    
    @database_sync_to_async
    def get_portfolio_data(self):
        """Get current portfolio data."""
        try:
            account = Account.objects.get(id=self.account_id, user=self.scope['user'])
            portfolio = Portfolio.objects.get(account=account)
            
            # Get positions with current market values
            positions = []
            for position in portfolio.positions.filter(quantity__gt=0):
                try:
                    symbol_obj = Symbol.objects.get(symbol=position.symbol)
                    latest_data = MarketData.objects.filter(symbol=symbol_obj, is_latest=True).first()
                    current_price = latest_data.price if latest_data else position.average_price
                    
                    market_value = position.quantity * current_price
                    unrealized_pnl = (current_price - position.average_price) * position.quantity
                    
                    positions.append({
                        'symbol': position.symbol,
                        'quantity': int(position.quantity),
                        'average_price': float(position.average_price),
                        'current_price': float(current_price),
                        'market_value': float(market_value),
                        'unrealized_pnl': float(unrealized_pnl),
                        'unrealized_pnl_percent': float((unrealized_pnl / (position.average_price * position.quantity)) * 100) if position.average_price > 0 else 0
                    })
                except Symbol.DoesNotExist:
                    continue
            
            return {
                'account_id': str(account.id),
                'account_name': account.name,
                'cash_balance': float(account.current_balance),
                'total_equity': float(account.equity),
                'total_value': float(portfolio.total_value),
                'day_change': float(portfolio.day_change),
                'day_change_percent': float(portfolio.day_change_percent),
                'positions': positions,
                'timestamp': portfolio.updated_at.isoformat()
            }
            
        except (Account.DoesNotExist, Portfolio.DoesNotExist):
            return None
    
    async def send_portfolio_update(self):
        """Send portfolio update to client."""
        portfolio_data = await self.get_portfolio_data()
        if portfolio_data:
            await self.send_json_data({
                'type': 'portfolio_update',
                'data': portfolio_data
            })


class OrderConsumer(TradingConsumerMixin, AsyncWebsocketConsumer):
    """Consumer for real-time order updates."""
    
    async def connect(self):
        """Handle order WebSocket connection."""
        self.account_id = self.scope['url_route']['kwargs']['account_id']
        self.group_name = f'orders_{self.account_id}'
        
        # Verify user has access to this account
        if not await self.verify_account_access():
            await self.close()
            return
        
        # Join orders group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await super().connect()
        
        # Send initial orders data
        await self.send_orders_update()
    
    async def disconnect(self, close_code):
        """Handle order WebSocket disconnection."""
        # Leave orders group
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'request_update':
                await self.send_orders_update()
            elif message_type == 'ping':
                await self.send_json_data({'type': 'pong'})
                
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")
    
    async def order_update(self, event):
        """Handle order update from group."""
        await self.send_json_data(event['data'])
    
    @database_sync_to_async
    def verify_account_access(self):
        """Verify user has access to the account."""
        try:
            Account.objects.get(id=self.account_id, user=self.scope['user'])
            return True
        except Account.DoesNotExist:
            return False
    
    @database_sync_to_async
    def get_orders_data(self):
        """Get current orders data."""
        try:
            account = Account.objects.get(id=self.account_id, user=self.scope['user'])
            
            # Get recent orders
            orders = []
            for order in account.orders.all().order_by('-created_at')[:20]:
                orders.append({
                    'id': str(order.id),
                    'symbol': order.symbol,
                    'order_type': order.order_type,
                    'side': order.side,
                    'quantity': int(order.quantity),
                    'filled_quantity': int(order.filled_quantity),
                    'price': float(order.price) if order.price else None,
                    'stop_price': float(order.stop_price) if order.stop_price else None,
                    'status': order.status,
                    'created_at': order.created_at.isoformat(),
                    'filled_at': order.filled_at.isoformat() if order.filled_at else None,
                    'total_cost': float(order.total_cost) if order.total_cost else None
                })
            
            return {
                'account_id': str(account.id),
                'orders': orders,
                'timestamp': account.updated_at.isoformat()
            }
            
        except Account.DoesNotExist:
            return None
    
    async def send_orders_update(self):
        """Send orders update to client."""
        orders_data = await self.get_orders_data()
        if orders_data:
            await self.send_json_data({
                'type': 'orders_update',
                'data': orders_data
            })


class RiskConsumer(TradingConsumerMixin, AsyncWebsocketConsumer):
    """Consumer for real-time risk management updates."""
    
    async def connect(self):
        """Handle risk WebSocket connection."""
        self.account_id = self.scope['url_route']['kwargs']['account_id']
        self.group_name = f'risk_{self.account_id}'
        
        # Verify user has access to this account
        if not await self.verify_account_access():
            await self.close()
            return
        
        # Join risk group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await super().connect()
        
        # Send initial risk data
        await self.send_risk_update()
    
    async def disconnect(self, close_code):
        """Handle risk WebSocket disconnection."""
        # Leave risk group
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'request_update':
                await self.send_risk_update()
            elif message_type == 'ping':
                await self.send_json_data({'type': 'pong'})
                
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")
    
    async def risk_update(self, event):
        """Handle risk update from group."""
        await self.send_json_data(event['data'])
    
    @database_sync_to_async
    def verify_account_access(self):
        """Verify user has access to the account."""
        try:
            Account.objects.get(id=self.account_id, user=self.scope['user'])
            return True
        except Account.DoesNotExist:
            return False
    
    @database_sync_to_async
    def get_risk_data(self):
        """Get current risk data."""
        try:
            account = Account.objects.get(id=self.account_id, user=self.scope['user'])
            
            # Calculate risk metrics
            risk_metrics = TradingRiskService.calculate_risk_metrics(account)
            risk_alerts = TradingRiskService.generate_risk_alerts(account)
            
            return {
                'account_id': str(account.id),
                'risk_metrics': {
                    'risk_score': float(risk_metrics['risk_score']),
                    'concentration_risk': float(risk_metrics['concentration_risk']),
                    'largest_position_percent': float(risk_metrics['largest_position_percent']),
                    'current_drawdown': float(risk_metrics['current_drawdown']),
                    'daily_loss_limit_reached': risk_metrics['daily_loss_limit_reached'],
                    'current_leverage': float(risk_metrics['current_leverage']),
                    'max_leverage': float(risk_metrics['max_leverage']),
                    'total_positions': risk_metrics['total_positions'],
                    'portfolio_value': float(risk_metrics['portfolio_value'])
                },
                'risk_alerts': risk_alerts,
                'timestamp': account.updated_at.isoformat()
            }
            
        except Account.DoesNotExist:
            return None
    
    async def send_risk_update(self):
        """Send risk update to client."""
        risk_data = await self.get_risk_data()
        if risk_data:
            await self.send_json_data({
                'type': 'risk_update',
                'data': risk_data
            })


class PriceConsumer(TradingConsumerMixin, AsyncWebsocketConsumer):
    """Consumer for real-time price updates."""
    
    async def connect(self):
        """Handle price WebSocket connection."""
        self.group_name = 'market_prices'
        
        # Join market prices group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await super().connect()
    
    async def disconnect(self, close_code):
        """Handle price WebSocket disconnection."""
        # Leave market prices group
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
        await super().disconnect(close_code)
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'subscribe':
                symbols = data.get('symbols', [])
                # Store subscribed symbols for this connection
                self.subscribed_symbols = symbols
            elif message_type == 'ping':
                await self.send_json_data({'type': 'pong'})
                
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")
    
    async def price_update(self, event):
        """Handle price update from group."""
        # Only send updates for subscribed symbols
        if hasattr(self, 'subscribed_symbols'):
            symbol = event['data'].get('symbol')
            if symbol in self.subscribed_symbols:
                await self.send_json_data(event['data'])
        else:
            # Send all updates if no specific subscription
            await self.send_json_data(event['data'])
