from django import forms
from django.contrib.auth.forms import UserCreationForm, PasswordResetForm, SetPasswordForm
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import authenticate
from .models import User


class CustomUserRegistrationForm(UserCreationForm):
    """
    Custom user registration form with trading-specific fields and enhanced validation.
    """
    
    # Basic Information
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your email address',
            'autocomplete': 'email'
        }),
        help_text='We will send a verification email to this address.'
    )
    
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your first name',
            'autocomplete': 'given-name'
        })
    )
    
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your last name',
            'autocomplete': 'family-name'
        })
    )
    
    phone_number = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+****************',
            'autocomplete': 'tel'
        }),
        help_text='Optional: For SMS notifications and account security.'
    )
    
    date_of_birth = forms.DateField(
        required=True,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
            'max': '2006-01-01'  # Must be at least 18 years old
        }),
        help_text='You must be at least 18 years old to register.'
    )
    
    # Trading Profile
    trading_experience = forms.ChoiceField(
        choices=User.TRADING_EXPERIENCE_CHOICES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        help_text='Select your trading experience level.'
    )
    
    risk_tolerance = forms.ChoiceField(
        choices=User.RISK_TOLERANCE_CHOICES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        help_text='Select your risk tolerance level.'
    )
    
    initial_balance = forms.DecimalField(
        max_digits=12,
        decimal_places=2,
        min_value=1000.00,
        max_value=1000000.00,
        initial=10000.00,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '100.00',
            'min': '1000.00',
            'max': '1000000.00'
        }),
        help_text='Starting virtual balance for trading simulation ($1,000 - $1,000,000).'
    )
    
    # Preferences
    email_notifications = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text='Receive email notifications about your trades and account.'
    )
    
    newsletter_subscription = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text='Subscribe to our trading newsletter and market updates.'
    )
    
    # Terms and Conditions
    terms_accepted = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text='You must accept the terms and conditions to register.',
        error_messages={
            'required': 'You must accept the terms and conditions to register.'
        }
    )
    
    # Password fields with custom styling
    password1 = forms.CharField(
        label=_("Password"),
        strip=False,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter a strong password',
            'autocomplete': 'new-password'
        }),
        help_text='Password must be at least 8 characters long and contain letters and numbers.'
    )
    
    password2 = forms.CharField(
        label=_("Password confirmation"),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirm your password',
            'autocomplete': 'new-password'
        }),
        strip=False,
        help_text='Enter the same password as before, for verification.'
    )

    class Meta:
        model = User
        fields = (
            'username', 'email', 'first_name', 'last_name', 'phone_number',
            'date_of_birth', 'trading_experience', 'risk_tolerance',
            'initial_balance', 'email_notifications', 'newsletter_subscription',
            'password1', 'password2'
        )
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Choose a username',
                'autocomplete': 'username'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add Bootstrap classes to username field
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Choose a username',
            'autocomplete': 'username'
        })
        
        # Set field order
        self.field_order = [
            'username', 'email', 'first_name', 'last_name', 'phone_number',
            'date_of_birth', 'trading_experience', 'risk_tolerance',
            'initial_balance', 'password1', 'password2', 'email_notifications',
            'newsletter_subscription', 'terms_accepted'
        ]

    def clean_email(self):
        """Validate email uniqueness."""
        email = self.cleaned_data.get('email')
        if email and User.objects.filter(email=email).exists():
            raise ValidationError('A user with this email address already exists.')
        return email

    def clean_username(self):
        """Validate username with custom rules."""
        username = self.cleaned_data.get('username')
        if username:
            # Check length
            if len(username) < 3:
                raise ValidationError('Username must be at least 3 characters long.')
            if len(username) > 30:
                raise ValidationError('Username must be no more than 30 characters long.')
            
            # Check for invalid characters
            if not username.replace('_', '').replace('-', '').isalnum():
                raise ValidationError('Username can only contain letters, numbers, hyphens, and underscores.')
            
            # Check if username already exists
            if User.objects.filter(username=username).exists():
                raise ValidationError('A user with this username already exists.')
        
        return username

    def clean_date_of_birth(self):
        """Validate age requirement."""
        from datetime import date, timedelta
        
        date_of_birth = self.cleaned_data.get('date_of_birth')
        if date_of_birth:
            today = date.today()
            age = today.year - date_of_birth.year - ((today.month, today.day) < (date_of_birth.month, date_of_birth.day))
            
            if age < 18:
                raise ValidationError('You must be at least 18 years old to register.')
            if age > 120:
                raise ValidationError('Please enter a valid date of birth.')
        
        return date_of_birth

    def clean_phone_number(self):
        """Validate phone number format if provided."""
        phone_number = self.cleaned_data.get('phone_number')
        if phone_number:
            # Remove all non-digit characters for validation
            digits_only = ''.join(filter(str.isdigit, phone_number))
            
            # Check if it's a valid length (10-15 digits)
            if len(digits_only) < 10 or len(digits_only) > 15:
                raise ValidationError('Please enter a valid phone number.')
        
        return phone_number

    def clean_password1(self):
        """Enhanced password validation."""
        password1 = self.cleaned_data.get('password1')
        if password1:
            # Use Django's built-in password validators
            validate_password(password1)
            
            # Additional custom validation
            if len(password1) < 8:
                raise ValidationError('Password must be at least 8 characters long.')
            
            if password1.isdigit():
                raise ValidationError('Password cannot be entirely numeric.')
            
            if not any(c.isalpha() for c in password1):
                raise ValidationError('Password must contain at least one letter.')
            
            if not any(c.isdigit() for c in password1):
                raise ValidationError('Password must contain at least one number.')
        
        return password1

    def save(self, commit=True):
        """Save the user with additional fields."""
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.phone_number = self.cleaned_data.get('phone_number', '')
        user.date_of_birth = self.cleaned_data['date_of_birth']
        user.trading_experience = self.cleaned_data['trading_experience']
        user.risk_tolerance = self.cleaned_data['risk_tolerance']
        user.initial_balance = self.cleaned_data['initial_balance']
        user.email_notifications = self.cleaned_data['email_notifications']
        user.newsletter_subscription = self.cleaned_data['newsletter_subscription']
        
        # Set email as unverified initially
        user.email_verified = False
        
        if commit:
            user.save()
        return user


class CustomLoginForm(forms.Form):
    """
    Enhanced login form with security features and better error handling.
    """

    username_or_email = forms.CharField(
        max_length=254,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Username or Email',
            'autocomplete': 'username',
            'autofocus': True
        }),
        error_messages={
            'required': 'Please enter your username or email address.',
            'max_length': 'Username or email is too long.'
        }
    )

    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Password',
            'autocomplete': 'current-password'
        }),
        error_messages={
            'required': 'Please enter your password.'
        }
    )

    remember_me = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='Keep me signed in for 2 weeks',
        help_text='Check this box to stay logged in on this device.'
    )

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

    def clean(self):
        """Authenticate user with enhanced security and error handling."""
        cleaned_data = super().clean()
        username_or_email = cleaned_data.get('username_or_email')
        password = cleaned_data.get('password')

        if username_or_email and password:
            # Check for too many failed attempts (basic rate limiting)
            if self.request:
                session_key = f'failed_login_attempts_{username_or_email.lower()}'
                failed_attempts = self.request.session.get(session_key, 0)

                if failed_attempts >= 5:
                    raise ValidationError(
                        'Too many failed login attempts. Please try again in 15 minutes or reset your password.'
                    )

            # Try to authenticate with username first
            user = authenticate(username=username_or_email, password=password)

            # If that fails, try with email
            if not user:
                try:
                    user_obj = User.objects.get(email__iexact=username_or_email)
                    user = authenticate(username=user_obj.username, password=password)
                except User.DoesNotExist:
                    pass

            if not user:
                # Increment failed attempts counter
                if self.request:
                    session_key = f'failed_login_attempts_{username_or_email.lower()}'
                    failed_attempts = self.request.session.get(session_key, 0) + 1
                    self.request.session[session_key] = failed_attempts
                    self.request.session.set_expiry(900)  # 15 minutes

                raise ValidationError(
                    'Invalid username/email or password. Please check your credentials and try again.'
                )

            if not user.is_active:
                raise ValidationError(
                    'Your account has been deactivated. Please contact support for assistance.'
                )

            # Check if email is verified (optional warning)
            if hasattr(user, 'email_verified') and not user.email_verified:
                # Don't block login, but we'll show a message in the view
                pass

            # Clear failed attempts on successful validation
            if self.request:
                session_key = f'failed_login_attempts_{username_or_email.lower()}'
                self.request.session.pop(session_key, None)

            cleaned_data['user'] = user

        return cleaned_data


class CustomPasswordResetForm(PasswordResetForm):
    """
    Custom password reset form with enhanced styling and validation.
    """

    email = forms.EmailField(
        max_length=254,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your email address',
            'autocomplete': 'email',
            'autofocus': True
        }),
        error_messages={
            'required': 'Please enter your email address.',
            'invalid': 'Please enter a valid email address.'
        }
    )

    def clean_email(self):
        """Validate that the email exists in our system."""
        email = self.cleaned_data.get('email')
        if email:
            if not User.objects.filter(email__iexact=email).exists():
                raise ValidationError(
                    'No account found with this email address. '
                    'Please check your email or create a new account.'
                )
        return email


class CustomSetPasswordForm(SetPasswordForm):
    """
    Custom set password form with enhanced styling and validation.
    """

    new_password1 = forms.CharField(
        label='New Password',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your new password',
            'autocomplete': 'new-password'
        }),
        strip=False,
        help_text='Password must be at least 8 characters long and contain letters and numbers.'
    )

    new_password2 = forms.CharField(
        label='Confirm New Password',
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirm your new password',
            'autocomplete': 'new-password'
        }),
        strip=False,
        help_text='Enter the same password as above, for verification.'
    )

    def clean_new_password1(self):
        """Enhanced password validation."""
        password = self.cleaned_data.get('new_password1')
        if password:
            # Check minimum length
            if len(password) < 8:
                raise ValidationError('Password must be at least 8 characters long.')

            # Check for at least one letter and one number
            if not any(c.isalpha() for c in password):
                raise ValidationError('Password must contain at least one letter.')

            if not any(c.isdigit() for c in password):
                raise ValidationError('Password must contain at least one number.')

            # Check against common passwords
            common_passwords = ['password', '12345678', 'qwerty', 'abc123']
            if password.lower() in common_passwords:
                raise ValidationError('This password is too common. Please choose a more secure password.')

        return password


class UserProfileForm(forms.ModelForm):
    """
    Form for editing user profile information.
    """

    date_of_birth = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
            'placeholder': 'Select your date of birth'
        }),
        help_text='Your date of birth (optional, used for age verification)'
    )

    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone_number',
            'date_of_birth', 'profile_picture'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your first name'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your last name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your email address'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your phone number'
            }),
            'profile_picture': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
        }
        help_texts = {
            'email': 'Your email address for account notifications',
            'phone_number': 'Your phone number for SMS notifications (optional)',
            'profile_picture': 'Upload a profile picture (JPG, PNG, max 5MB)'
        }

    def clean_profile_picture(self):
        """Validate profile picture upload."""
        picture = self.cleaned_data.get('profile_picture')

        if picture:
            # Only validate new uploads (UploadedFile objects), not existing ImageFieldFile
            if hasattr(picture, 'content_type') and hasattr(picture, 'read'):
                # Check file size (5MB limit)
                if picture.size > 5 * 1024 * 1024:
                    raise ValidationError('Profile picture must be smaller than 5MB.')

                # Check file type
                allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
                if picture.content_type not in allowed_types:
                    raise ValidationError('Profile picture must be a JPG, PNG, or GIF image.')

            # For existing files (ImageFieldFile), we trust they were validated when uploaded
            # and only validate new uploads

        return picture

    def clean_phone_number(self):
        """Validate phone number format."""
        phone = self.cleaned_data.get('phone_number')

        if phone:
            # Remove common formatting characters
            cleaned_phone = ''.join(filter(str.isdigit, phone))

            # Check length (US phone numbers)
            if len(cleaned_phone) < 10 or len(cleaned_phone) > 15:
                raise ValidationError('Please enter a valid phone number.')

        return phone


class TradingPreferencesForm(forms.ModelForm):
    """
    Form for editing trading preferences and settings.
    """

    class Meta:
        model = User
        fields = [
            'trading_experience', 'risk_tolerance', 'initial_balance',
            'email_notifications', 'sms_notifications', 'newsletter_subscription'
        ]
        widgets = {
            'trading_experience': forms.Select(attrs={
                'class': 'form-select'
            }),
            'risk_tolerance': forms.Select(attrs={
                'class': 'form-select'
            }),
            'initial_balance': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1000',
                'max': '1000000',
                'step': '100'
            }),
            'email_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'sms_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'newsletter_subscription': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        help_texts = {
            'trading_experience': 'Select your trading experience level',
            'risk_tolerance': 'Choose your preferred risk level for trading',
            'initial_balance': 'Your starting virtual trading balance (USD)',
            'email_notifications': 'Receive email notifications for trading activities',
            'sms_notifications': 'Receive SMS notifications for important alerts',
            'newsletter_subscription': 'Subscribe to trading newsletter and market updates'
        }

    def clean_initial_balance(self):
        """Validate initial balance amount."""
        balance = self.cleaned_data.get('initial_balance')

        if balance:
            if balance < 1000:
                raise ValidationError('Initial balance must be at least $1,000.')
            if balance > 1000000:
                raise ValidationError('Initial balance cannot exceed $1,000,000.')

        return balance
