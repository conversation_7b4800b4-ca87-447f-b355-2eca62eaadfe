/**
 * WebSocket Manager for Real-time Trading Updates
 * Handles WebSocket connections for portfolio, orders, risk, and price updates
 */

class WebSocketManager {
    constructor() {
        this.connections = new Map();
        this.reconnectAttempts = new Map();
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.heartbeatInterval = 30000; // 30 seconds
        this.heartbeatTimers = new Map();
        
        // Bind methods to preserve context
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
        this.handleOnline = this.handleOnline.bind(this);
        this.handleOffline = this.handleOffline.bind(this);
        
        // Listen for page visibility and network changes
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
        window.addEventListener('online', this.handleOnline);
        window.addEventListener('offline', this.handleOffline);
    }
    
    /**
     * Connect to a WebSocket endpoint
     */
    connect(name, url, options = {}) {
        if (this.connections.has(name)) {
            console.warn(`WebSocket connection '${name}' already exists`);
            return this.connections.get(name);
        }
        
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${wsProtocol}//${window.location.host}${url}`;
        
        console.log(`Connecting to WebSocket: ${name} at ${wsUrl}`);
        
        const ws = new WebSocket(wsUrl);
        const connectionInfo = {
            ws: ws,
            url: url,
            options: options,
            isConnected: false,
            lastActivity: Date.now()
        };
        
        // Set up event handlers
        ws.onopen = (event) => this.handleOpen(name, event);
        ws.onmessage = (event) => this.handleMessage(name, event);
        ws.onclose = (event) => this.handleClose(name, event);
        ws.onerror = (event) => this.handleError(name, event);
        
        this.connections.set(name, connectionInfo);
        this.reconnectAttempts.set(name, 0);
        
        // Start heartbeat
        this.startHeartbeat(name);
        
        return connectionInfo;
    }
    
    /**
     * Disconnect from a WebSocket
     */
    disconnect(name) {
        const connection = this.connections.get(name);
        if (connection) {
            this.stopHeartbeat(name);
            connection.ws.close();
            this.connections.delete(name);
            this.reconnectAttempts.delete(name);
            console.log(`Disconnected from WebSocket: ${name}`);
        }
    }
    
    /**
     * Disconnect all WebSockets
     */
    disconnectAll() {
        for (const name of this.connections.keys()) {
            this.disconnect(name);
        }
    }
    
    /**
     * Send message to a WebSocket
     */
    send(name, data) {
        const connection = this.connections.get(name);
        if (connection && connection.isConnected) {
            connection.ws.send(JSON.stringify(data));
            connection.lastActivity = Date.now();
            return true;
        }
        console.warn(`Cannot send message to '${name}': not connected`);
        return false;
    }
    
    /**
     * Handle WebSocket open event
     */
    handleOpen(name, event) {
        console.log(`WebSocket connected: ${name}`);
        const connection = this.connections.get(name);
        if (connection) {
            connection.isConnected = true;
            connection.lastActivity = Date.now();
            this.reconnectAttempts.set(name, 0);
            
            // Trigger custom open event
            if (connection.options.onOpen) {
                connection.options.onOpen(event);
            }
            
            // Dispatch custom event
            this.dispatchEvent('websocket-connected', { name, event });
        }
    }
    
    /**
     * Handle WebSocket message event
     */
    handleMessage(name, event) {
        const connection = this.connections.get(name);
        if (connection) {
            connection.lastActivity = Date.now();
            
            try {
                const data = JSON.parse(event.data);
                
                // Handle pong responses
                if (data.type === 'pong') {
                    return;
                }
                
                // Trigger custom message handler
                if (connection.options.onMessage) {
                    connection.options.onMessage(data, event);
                }
                
                // Dispatch custom event
                this.dispatchEvent('websocket-message', { name, data, event });
                
            } catch (error) {
                console.error(`Error parsing WebSocket message from '${name}':`, error);
            }
        }
    }
    
    /**
     * Handle WebSocket close event
     */
    handleClose(name, event) {
        console.log(`WebSocket closed: ${name}`, event.code, event.reason);
        const connection = this.connections.get(name);
        if (connection) {
            connection.isConnected = false;
            
            // Trigger custom close handler
            if (connection.options.onClose) {
                connection.options.onClose(event);
            }
            
            // Dispatch custom event
            this.dispatchEvent('websocket-disconnected', { name, event });
            
            // Attempt reconnection if not a clean close
            if (event.code !== 1000 && !document.hidden) {
                this.attemptReconnect(name);
            }
        }
    }
    
    /**
     * Handle WebSocket error event
     */
    handleError(name, event) {
        console.error(`WebSocket error: ${name}`, event);
        const connection = this.connections.get(name);
        if (connection && connection.options.onError) {
            connection.options.onError(event);
        }
        
        // Dispatch custom event
        this.dispatchEvent('websocket-error', { name, event });
    }
    
    /**
     * Attempt to reconnect to a WebSocket
     */
    attemptReconnect(name) {
        const attempts = this.reconnectAttempts.get(name) || 0;
        
        if (attempts >= this.maxReconnectAttempts) {
            console.error(`Max reconnection attempts reached for '${name}'`);
            return;
        }
        
        const delay = this.reconnectDelay * Math.pow(2, attempts); // Exponential backoff
        console.log(`Attempting to reconnect '${name}' in ${delay}ms (attempt ${attempts + 1})`);
        
        setTimeout(() => {
            const connection = this.connections.get(name);
            if (connection) {
                this.reconnectAttempts.set(name, attempts + 1);
                this.disconnect(name);
                this.connect(name, connection.url, connection.options);
            }
        }, delay);
    }
    
    /**
     * Start heartbeat for a connection
     */
    startHeartbeat(name) {
        const timer = setInterval(() => {
            this.send(name, { type: 'ping' });
        }, this.heartbeatInterval);
        
        this.heartbeatTimers.set(name, timer);
    }
    
    /**
     * Stop heartbeat for a connection
     */
    stopHeartbeat(name) {
        const timer = this.heartbeatTimers.get(name);
        if (timer) {
            clearInterval(timer);
            this.heartbeatTimers.delete(name);
        }
    }
    
    /**
     * Handle page visibility change
     */
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('Page hidden, pausing WebSocket heartbeats');
            // Optionally pause heartbeats or close connections
        } else {
            console.log('Page visible, resuming WebSocket connections');
            // Reconnect any closed connections
            for (const [name, connection] of this.connections.entries()) {
                if (!connection.isConnected) {
                    this.attemptReconnect(name);
                }
            }
        }
    }
    
    /**
     * Handle online event
     */
    handleOnline() {
        console.log('Network online, checking WebSocket connections');
        for (const [name, connection] of this.connections.entries()) {
            if (!connection.isConnected) {
                this.attemptReconnect(name);
            }
        }
    }
    
    /**
     * Handle offline event
     */
    handleOffline() {
        console.log('Network offline, WebSocket connections may be affected');
    }
    
    /**
     * Dispatch custom event
     */
    dispatchEvent(eventType, detail) {
        const event = new CustomEvent(eventType, { detail });
        document.dispatchEvent(event);
    }
    
    /**
     * Get connection status
     */
    getStatus(name) {
        const connection = this.connections.get(name);
        return connection ? {
            isConnected: connection.isConnected,
            lastActivity: connection.lastActivity,
            reconnectAttempts: this.reconnectAttempts.get(name) || 0
        } : null;
    }
    
    /**
     * Get all connection statuses
     */
    getAllStatuses() {
        const statuses = {};
        for (const name of this.connections.keys()) {
            statuses[name] = this.getStatus(name);
        }
        return statuses;
    }
}

// Create global WebSocket manager instance
window.wsManager = new WebSocketManager();

// Clean up on page unload
window.addEventListener('beforeunload', () => {
    window.wsManager.disconnectAll();
});
