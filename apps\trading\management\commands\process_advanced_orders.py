"""
Management command to process advanced order types (trailing stops, conditionals, etc.).
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.trading.services import OrderExecutionService
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Process advanced order types including trailing stops and conditional orders'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode without executing orders',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )

    def handle(self, *args, **options):
        """Process advanced orders."""
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        start_time = timezone.now()
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('Running in DRY-RUN mode - no orders will be executed')
            )
        
        if verbose:
            self.stdout.write(f'Starting advanced order processing at {start_time}')
        
        try:
            # Process advanced orders
            if not dry_run:
                results = OrderExecutionService.process_advanced_orders()
            else:
                # In dry-run mode, just count orders that would be processed
                from apps.trading.models import Order
                
                trailing_orders = Order.objects.filter(
                    status__in=['pending', 'partially_filled'],
                    order_type='trailing_stop'
                ).count()
                
                conditional_orders = Order.objects.filter(
                    status__in=['pending', 'partially_filled'],
                    order_type='conditional'
                ).count()
                
                results = {
                    'processed': trailing_orders + conditional_orders,
                    'executed': 0,
                    'updated': 0
                }
            
            # Display results
            self.stdout.write(
                self.style.SUCCESS(
                    f'Advanced order processing completed:\n'
                    f'  - Orders processed: {results["processed"]}\n'
                    f'  - Orders executed: {results["executed"]}\n'
                    f'  - Orders updated: {results["updated"]}'
                )
            )
            
            if verbose:
                end_time = timezone.now()
                duration = (end_time - start_time).total_seconds()
                self.stdout.write(f'Processing completed in {duration:.2f} seconds')
                
        except Exception as e:
            logger.error(f'Error processing advanced orders: {str(e)}')
            self.stdout.write(
                self.style.ERROR(f'Error processing advanced orders: {str(e)}')
            )
            raise
