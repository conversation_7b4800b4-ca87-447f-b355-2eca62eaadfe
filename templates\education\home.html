{% extends 'base.html' %}
{% load static %}

{% block title %}Trading Education Center - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
.education-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin-bottom: 40px;
}

.learning-path-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.learning-path-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.difficulty-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.difficulty-beginner { background-color: #28a745; }
.difficulty-intermediate { background-color: #ffc107; color: #212529; }
.difficulty-advanced { background-color: #fd7e14; }
.difficulty-expert { background-color: #dc3545; }

.category-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-3px);
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.progress-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="education-hero">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="fas fa-graduation-cap me-3"></i>
                    Trading Education Center
                </h1>
                <p class="lead mb-4">
                    Master the art of trading with our comprehensive learning paths, interactive tutorials, and hands-on simulations.
                </p>
                <a href="{% url 'education:learning_paths' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-play me-2"></i>Start Learning
                </a>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-chart-line" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- User Progress Overview -->
    {% if recent_progress %}
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-chart-bar me-2"></i>Your Learning Progress
            </h2>
        </div>
        
        <!-- Stats Cards -->
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ completed_paths }}</div>
                <div class="stats-label">Completed Paths</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ recent_progress|length }}</div>
                <div class="stats-label">In Progress</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ total_time }}</div>
                <div class="stats-label">Minutes Learned</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">
                    <a href="{% url 'education:progress_dashboard' %}" class="text-white text-decoration-none">
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                <div class="stats-label">View Dashboard</div>
            </div>
        </div>
        
        <!-- Recent Progress -->
        <div class="col-12 mt-4">
            <div class="progress-card">
                <h5 class="mb-3">Continue Learning</h5>
                <div class="row">
                    {% for progress in recent_progress %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">{{ progress.learning_path.title }}</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ progress.completion_percentage }}%"
                                         aria-valuenow="{{ progress.completion_percentage }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ progress.completion_percentage }}%
                                    </div>
                                </div>
                                <a href="{% url 'education:learning_path_detail' progress.learning_path.id %}" 
                                   class="btn btn-sm btn-primary">Continue</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Featured Learning Paths -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-star me-2"></i>Featured Learning Paths
            </h2>
        </div>
        
        {% for path in featured_paths %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card learning-path-card">
                {% if path.thumbnail %}
                <img src="{{ path.thumbnail }}" class="card-img-top" alt="{{ path.title }}" style="height: 200px; object-fit: cover;">
                {% else %}
                <div class="card-img-top d-flex align-items-center justify-content-center" 
                     style="height: 200px; background: linear-gradient(135deg, {{ path.category.color }}22 0%, {{ path.category.color }}44 100%);">
                    <i class="fas {{ path.category.icon|default:'fa-book' }}" style="font-size: 4rem; color: {{ path.category.color }};"></i>
                </div>
                {% endif %}
                
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <span class="badge difficulty-{{ path.difficulty_level }} difficulty-badge">
                            {{ path.get_difficulty_level_display }}
                        </span>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>{{ path.estimated_duration }} min
                        </small>
                    </div>
                    
                    <h5 class="card-title">{{ path.title }}</h5>
                    <p class="card-text">{{ path.description|truncatewords:20 }}</p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-list me-1"></i>{{ path.total_lessons }} lessons
                        </small>
                        <a href="{% url 'education:learning_path_detail' path.id %}" class="btn btn-primary">
                            Start Learning
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                No featured learning paths available at the moment. Check back soon!
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Categories -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-th-large me-2"></i>Learning Categories
            </h2>
        </div>
        
        {% for category in categories %}
        <div class="col-md-6 col-lg-3 mb-4">
            <a href="{% url 'education:learning_paths' %}?category={{ category.id }}" class="text-decoration-none">
                <div class="category-card">
                    <div class="category-icon" style="color: {{ category.color }};">
                        <i class="fas {{ category.icon|default:'fa-book' }}"></i>
                    </div>
                    <h5 class="mb-2">{{ category.name }}</h5>
                    <p class="text-muted mb-2">{{ category.description|truncatewords:10 }}</p>
                    <small class="text-muted">{{ category.path_count }} learning paths</small>
                </div>
            </a>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Learning categories are being prepared. Check back soon!
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Call to Action -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px;">
                <h3 class="mb-3">Ready to Start Your Trading Journey?</h3>
                <p class="lead mb-4">Join thousands of traders who have improved their skills with our comprehensive education platform.</p>
                <a href="{% url 'education:learning_paths' %}" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-rocket me-2"></i>Browse All Paths
                </a>
                <a href="{% url 'education:progress_dashboard' %}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-chart-line me-2"></i>View My Progress
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any interactive features here
document.addEventListener('DOMContentLoaded', function() {
    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.transition = 'width 1s ease-in-out';
            bar.style.width = width;
        }, 100);
    });
});
</script>
{% endblock %}
