{% extends 'base.html' %}
{% load static %}

{% block title %}Performance Dashboard - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.performance-chart {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.optimization-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-working { background-color: #28a745; }
.status-error { background-color: #dc3545; }
.status-unknown { background-color: #6c757d; }

.btn-optimize {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    margin: 5px;
}

.btn-optimize:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-tachometer-alt me-2"></i>Performance Dashboard</h1>
                <div>
                    <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ db_metrics.database_size_mb|default:"N/A" }} MB</div>
                <div class="metric-label">Database Size</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ db_metrics.total_records|default:"N/A" }}</div>
                <div class="metric-label">Total Records</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value" id="cache-status">
                    {% if cache_stats.default.status == 'working' %}
                        <i class="fas fa-check-circle text-success"></i>
                    {% else %}
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                    {% endif %}
                </div>
                <div class="metric-label">Cache Status</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ current_time|date:"H:i" }}</div>
                <div class="metric-label">Last Updated</div>
            </div>
        </div>
    </div>

    <!-- Database Optimization -->
    <div class="row">
        <div class="col-md-6">
            <div class="optimization-card">
                <h5><i class="fas fa-database me-2"></i>Database Optimization</h5>
                <p class="text-muted">Optimize database performance and analyze query efficiency.</p>
                
                <div class="mb-3">
                    <button class="btn btn-optimize" onclick="performDatabaseOperation('analyze')">
                        <i class="fas fa-search me-2"></i>Analyze Performance
                    </button>
                    <button class="btn btn-optimize" onclick="performDatabaseOperation('optimize')">
                        <i class="fas fa-cogs me-2"></i>Optimize Database
                    </button>
                    <button class="btn btn-optimize" onclick="performDatabaseOperation('cleanup')">
                        <i class="fas fa-trash-alt me-2"></i>Cleanup Old Data
                    </button>
                </div>

                <div id="db-operation-result" class="mt-3"></div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="optimization-card">
                <h5><i class="fas fa-memory me-2"></i>Cache Management</h5>
                <p class="text-muted">Manage application cache for optimal performance.</p>
                
                <div class="mb-3">
                    <button class="btn btn-optimize" onclick="performCacheOperation('warm_cache')">
                        <i class="fas fa-fire me-2"></i>Warm Cache
                    </button>
                    <button class="btn btn-optimize" onclick="performCacheOperation('clear_all')">
                        <i class="fas fa-eraser me-2"></i>Clear All Cache
                    </button>
                    <button class="btn btn-optimize" onclick="performCacheOperation('stats')">
                        <i class="fas fa-chart-bar me-2"></i>Cache Statistics
                    </button>
                </div>

                <div id="cache-operation-result" class="mt-3"></div>
            </div>
        </div>
    </div>

    <!-- Database Tables Statistics -->
    {% if db_metrics.table_statistics %}
    <div class="row">
        <div class="col-12">
            <div class="performance-chart">
                <h5><i class="fas fa-table me-2"></i>Database Tables</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Table Name</th>
                                <th>Record Count</th>
                                <th>Percentage</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for table, count in db_metrics.table_statistics.items %}
                            <tr>
                                <td>{{ table }}</td>
                                <td>{{ count|floatformat:0 }}</td>
                                <td>
                                    {% widthratio count db_metrics.total_records 100 %}%
                                </td>
                                <td>
                                    {% if count > 1000 %}
                                        <span class="badge bg-warning">Large</span>
                                    {% elif count > 100 %}
                                        <span class="badge bg-info">Medium</span>
                                    {% else %}
                                        <span class="badge bg-success">Small</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Query Analysis -->
    {% if query_analysis.recommendations %}
    <div class="row">
        <div class="col-12">
            <div class="optimization-card">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Optimization Recommendations</h5>
                {% for rec in query_analysis.recommendations %}
                <div class="alert alert-warning">
                    <strong>{{ rec.type|title }}:</strong> {{ rec.message }}
                    {% if rec.action %}
                    <br><small><strong>Action:</strong> {{ rec.action }}</small>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Error Display -->
    {% if error %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>Error:</strong> {{ error }}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function performDatabaseOperation(operation) {
    const resultDiv = document.getElementById('db-operation-result');
    const button = event.target;
    
    // Show loading state
    button.classList.add('loading');
    button.disabled = true;
    
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Processing...</div>';
    
    const formData = new FormData();
    formData.append('operation', operation);
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    if (operation === 'cleanup') {
        const days = prompt('How many days of data to keep?', '90');
        if (days === null) {
            button.classList.remove('loading');
            button.disabled = false;
            resultDiv.innerHTML = '';
            return;
        }
        formData.append('days_to_keep', days);
    }
    
    fetch('{% url "trading:database_optimization_api" %}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `<div class="alert alert-success"><i class="fas fa-check me-2"></i>${data.message}</div>`;
            if (data.data) {
                resultDiv.innerHTML += `<pre class="mt-2">${JSON.stringify(data.data, null, 2)}</pre>`;
            }
        } else {
            resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times me-2"></i>${data.message}</div>`;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times me-2"></i>Error: ${error.message}</div>`;
    })
    .finally(() => {
        button.classList.remove('loading');
        button.disabled = false;
    });
}

function performCacheOperation(operation) {
    const resultDiv = document.getElementById('cache-operation-result');
    const button = event.target;
    
    // Show loading state
    button.classList.add('loading');
    button.disabled = true;
    
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Processing...</div>';
    
    const formData = new FormData();
    formData.append('operation', operation);
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    fetch('{% url "trading:cache_management_api" %}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `<div class="alert alert-success"><i class="fas fa-check me-2"></i>${data.message}</div>`;
            if (data.data) {
                resultDiv.innerHTML += `<pre class="mt-2">${JSON.stringify(data.data, null, 2)}</pre>`;
            }
        } else {
            resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times me-2"></i>${data.message}</div>`;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times me-2"></i>Error: ${error.message}</div>`;
    })
    .finally(() => {
        button.classList.remove('loading');
        button.disabled = false;
    });
}

function refreshDashboard() {
    location.reload();
}

// Auto-refresh every 5 minutes
setInterval(refreshDashboard, 300000);
</script>
{% endblock %}
