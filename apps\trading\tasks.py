"""
Celery tasks for trading operations.
"""

import logging
from celery import shared_task
from django.utils import timezone
from django.db import transaction
from decimal import Decimal
from .models import Order, Trade, Account, Portfolio, Position, RiskManagement
from .services import OrderExecutionService, TradingRiskService
from .signals import notify_portfolio_update, notify_order_update, notify_risk_update
from apps.market_data.models import MarketData, Symbol
from apps.accounts.models import Notification

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_pending_orders(self):
    """
    Process all pending orders that can be executed.
    """
    try:
        logger.info("Starting pending orders processing")
        
        # Get all pending orders
        pending_orders = Order.objects.filter(
            status__in=['pending', 'partially_filled']
        ).select_related('account')
        
        processed_count = 0
        executed_count = 0
        
        for order in pending_orders:
            try:
                processed_count += 1
                
                # Process based on order type
                if order.order_type == 'market':
                    if OrderExecutionService.execute_market_order(order):
                        executed_count += 1
                elif order.order_type == 'limit':
                    if OrderExecutionService.execute_limit_order(order):
                        executed_count += 1
                elif order.order_type in ['stop', 'stop_limit']:
                    if OrderExecutionService.execute_stop_order(order):
                        executed_count += 1
                
                # Send real-time updates
                notify_order_update(order.account.id)
                
            except Exception as e:
                logger.error(f"Error processing order {order.id}: {str(e)}")
                continue
        
        logger.info(f"Processed {processed_count} orders, executed {executed_count}")
        
        return {
            'processed': processed_count,
            'executed': executed_count,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error in process_pending_orders: {str(exc)}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True, max_retries=3)
def process_advanced_orders(self):
    """
    Process advanced order types (trailing stops, conditionals, etc.).
    """
    try:
        logger.info("Starting advanced orders processing")
        
        results = OrderExecutionService.process_advanced_orders()
        
        # Send updates for affected accounts
        affected_accounts = set()
        
        # Get accounts with trailing stop orders
        trailing_orders = Order.objects.filter(
            status__in=['pending', 'partially_filled'],
            order_type='trailing_stop'
        ).values_list('account_id', flat=True)
        affected_accounts.update(trailing_orders)
        
        # Get accounts with conditional orders
        conditional_orders = Order.objects.filter(
            status__in=['pending', 'partially_filled'],
            order_type='conditional'
        ).values_list('account_id', flat=True)
        affected_accounts.update(conditional_orders)
        
        # Send real-time updates
        for account_id in affected_accounts:
            notify_order_update(account_id)
            notify_portfolio_update(account_id)
        
        logger.info(f"Advanced orders processing completed: {results}")
        
        return results
        
    except Exception as exc:
        logger.error(f"Error in process_advanced_orders: {str(exc)}")
        raise self.retry(exc=exc, countdown=30)


@shared_task(bind=True, max_retries=3)
def update_portfolio_values(self):
    """
    Update portfolio values based on current market prices.
    """
    try:
        logger.info("Starting portfolio values update")
        
        updated_count = 0
        
        # Get all active portfolios
        portfolios = Portfolio.objects.select_related('account').all()
        
        for portfolio in portfolios:
            try:
                with transaction.atomic():
                    # Calculate new portfolio value
                    total_value = portfolio.account.current_balance
                    day_change = Decimal('0.00')
                    
                    # Get positions with current market prices
                    for position in portfolio.positions.filter(quantity__gt=0):
                        try:
                            symbol_obj = Symbol.objects.get(symbol=position.symbol)
                            latest_data = MarketData.objects.filter(
                                symbol=symbol_obj, 
                                is_latest=True
                            ).first()
                            
                            if latest_data:
                                # Update position current price
                                old_price = position.current_price
                                position.current_price = latest_data.price
                                position.save()
                                
                                # Add to total value
                                position_value = position.quantity * latest_data.price
                                total_value += position_value
                                
                                # Calculate day change for this position
                                if old_price and old_price != latest_data.price:
                                    price_change = latest_data.price - old_price
                                    position_day_change = price_change * position.quantity
                                    day_change += position_day_change
                                
                        except Symbol.DoesNotExist:
                            continue
                    
                    # Update portfolio
                    old_total = portfolio.total_value
                    portfolio.total_value = total_value
                    portfolio.day_change = day_change
                    
                    if old_total > 0:
                        portfolio.day_change_percent = (day_change / old_total) * Decimal('100')
                    else:
                        portfolio.day_change_percent = Decimal('0.00')
                    
                    portfolio.save()
                    
                    # Send real-time update
                    notify_portfolio_update(portfolio.account.id)
                    
                    updated_count += 1
                    
            except Exception as e:
                logger.error(f"Error updating portfolio {portfolio.id}: {str(e)}")
                continue
        
        logger.info(f"Updated {updated_count} portfolios")
        
        return {
            'updated': updated_count,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error in update_portfolio_values: {str(exc)}")
        raise self.retry(exc=exc, countdown=120)


@shared_task(bind=True, max_retries=3)
def send_risk_alerts(self):
    """
    Check risk metrics and send alerts for accounts that exceed limits.
    """
    try:
        logger.info("Starting risk alerts check")
        
        alerts_sent = 0
        
        # Get all active accounts
        accounts = Account.objects.filter(status='active')
        
        for account in accounts:
            try:
                # Generate risk alerts
                risk_alerts = TradingRiskService.generate_risk_alerts(account)
                
                if risk_alerts:
                    # Create notifications for high-priority alerts
                    for alert in risk_alerts:
                        if alert['type'] in ['danger', 'warning']:
                            Notification.objects.create(
                                user=account.user,
                                title=f"Risk Alert: {alert['title']}",
                                message=alert['message'],
                                notification_type=alert['type']
                            )
                            alerts_sent += 1
                    
                    # Send real-time risk update
                    notify_risk_update(account.id)
                
            except Exception as e:
                logger.error(f"Error checking risk for account {account.id}: {str(e)}")
                continue
        
        logger.info(f"Sent {alerts_sent} risk alerts")
        
        return {
            'alerts_sent': alerts_sent,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error in send_risk_alerts: {str(exc)}")
        raise self.retry(exc=exc, countdown=300)


@shared_task(bind=True)
def execute_order_async(self, order_id):
    """
    Execute a specific order asynchronously.
    """
    try:
        order = Order.objects.get(id=order_id)
        
        # Execute based on order type
        success = False
        if order.order_type == 'market':
            success = OrderExecutionService.execute_market_order(order)
        elif order.order_type == 'limit':
            success = OrderExecutionService.execute_limit_order(order)
        elif order.order_type in ['stop', 'stop_limit']:
            success = OrderExecutionService.execute_stop_order(order)
        elif order.order_type == 'trailing_stop':
            success = OrderExecutionService.execute_trailing_stop_order(order)
        elif order.order_type == 'conditional':
            success = OrderExecutionService.execute_conditional_order(order)
        
        if success:
            # Send real-time updates
            notify_order_update(order.account.id)
            notify_portfolio_update(order.account.id)
            notify_risk_update(order.account.id)
            
            logger.info(f"Order {order_id} executed successfully")
        else:
            logger.warning(f"Order {order_id} execution failed")
        
        return {
            'order_id': str(order_id),
            'success': success,
            'timestamp': timezone.now().isoformat()
        }
        
    except Order.DoesNotExist:
        logger.error(f"Order {order_id} not found")
        return {'error': 'Order not found'}
    except Exception as exc:
        logger.error(f"Error executing order {order_id}: {str(exc)}")
        raise


@shared_task(bind=True)
def update_trailing_stops(self):
    """
    Update trailing stop prices based on current market conditions.
    """
    try:
        logger.info("Starting trailing stops update")
        
        updated_count = 0
        
        # Get all active trailing stop orders
        trailing_orders = Order.objects.filter(
            status__in=['pending', 'partially_filled'],
            order_type='trailing_stop'
        )
        
        for order in trailing_orders:
            try:
                # Get current market price
                current_price = OrderExecutionService.get_current_price(order.symbol)
                if current_price:
                    # Update trailing stop
                    if order.update_trailing_stop(current_price):
                        updated_count += 1
                        
                        # Send real-time update
                        notify_order_update(order.account.id)
                
            except Exception as e:
                logger.error(f"Error updating trailing stop {order.id}: {str(e)}")
                continue
        
        logger.info(f"Updated {updated_count} trailing stops")
        
        return {
            'updated': updated_count,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error in update_trailing_stops: {str(exc)}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True)
def cleanup_expired_orders(self):
    """
    Clean up expired orders and send notifications.
    """
    try:
        logger.info("Starting expired orders cleanup")
        
        expired_count = 0
        
        # Get orders that should be expired
        expired_orders = Order.objects.filter(
            status__in=['pending', 'partially_filled'],
            time_in_force='day',
            created_at__date__lt=timezone.now().date()
        )
        
        for order in expired_orders:
            try:
                order.status = 'expired'
                order.notes = f"{order.notes}\nExpired on {timezone.now().date()}"
                order.save()
                
                # Create notification
                Notification.objects.create(
                    user=order.account.user,
                    title='Order Expired',
                    message=f'Your {order.get_order_type_display()} order for {order.quantity} shares of {order.symbol} has expired.',
                    notification_type='info'
                )
                
                # Send real-time update
                notify_order_update(order.account.id)
                
                expired_count += 1
                
            except Exception as e:
                logger.error(f"Error expiring order {order.id}: {str(e)}")
                continue
        
        logger.info(f"Expired {expired_count} orders")
        
        return {
            'expired': expired_count,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error in cleanup_expired_orders: {str(exc)}")
        raise self.retry(exc=exc, countdown=3600)
