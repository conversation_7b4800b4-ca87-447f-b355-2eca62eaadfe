"""
Trading services for order execution and management.
"""

import logging
from decimal import Decimal
from django.utils import timezone
from django.db import transaction
from typing import Optional, Dict
from .models import Order, Trade
from apps.market_data.models import Symbol, MarketData

logger = logging.getLogger(__name__)


class OrderExecutionService:
    """
    Service for executing trading orders.
    """
    
    @staticmethod
    def execute_market_order(order: Order) -> bool:
        """
        Execute a market order immediately at current market price.
        """
        try:
            with transaction.atomic():
                # Get current market price
                current_price = OrderExecutionService.get_current_price(order.symbol)
                if not current_price:
                    order.status = 'rejected'
                    order.notes = f"{order.notes}\nRejected: No market data available"
                    order.save()
                    return False
                
                # Apply slippage simulation (0.1% for market orders)
                slippage_factor = Decimal('0.001')  # 0.1%
                if order.side == 'buy':
                    execution_price = current_price * (Decimal('1') + slippage_factor)
                else:
                    execution_price = current_price * (Decimal('1') - slippage_factor)
                
                # Validate account balance for buy orders
                if order.side == 'buy':
                    total_cost = Decimal(str(order.quantity)) * execution_price + order.commission
                    if total_cost > order.account.current_balance:
                        order.status = 'rejected'
                        order.notes = f"{order.notes}\nRejected: Insufficient funds"
                        order.save()
                        return False
                
                # Validate position for sell orders
                if order.side == 'sell':
                    if not OrderExecutionService.validate_sell_position(order):
                        order.status = 'rejected'
                        order.notes = f"{order.notes}\nRejected: Insufficient shares to sell"
                        order.save()
                        return False
                
                # Create trade execution
                trade = Trade.objects.create(
                    order=order,
                    symbol=order.symbol,
                    side=order.side,
                    quantity=order.quantity,
                    price=execution_price,
                    execution_venue='SIMULATOR'
                )
                
                logger.info(f"Market order {order.id} executed: {trade.quantity} shares at ${trade.price}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to execute market order {order.id}: {str(e)}")
            order.status = 'rejected'
            order.notes = f"{order.notes}\nRejected: Execution error - {str(e)}"
            order.save()
            return False
    
    @staticmethod
    def execute_limit_order(order: Order) -> bool:
        """
        Execute a limit order if price conditions are met.
        """
        try:
            current_price = OrderExecutionService.get_current_price(order.symbol)
            if not current_price:
                return False
            
            # Check if limit order can be executed
            can_execute = False
            if order.side == 'buy' and current_price <= order.price:
                can_execute = True
            elif order.side == 'sell' and current_price >= order.price:
                can_execute = True
            
            if not can_execute:
                return False
            
            with transaction.atomic():
                # Validate account balance for buy orders
                if order.side == 'buy':
                    total_cost = Decimal(str(order.quantity)) * order.price + order.commission
                    if total_cost > order.account.current_balance:
                        order.status = 'rejected'
                        order.notes = f"{order.notes}\nRejected: Insufficient funds"
                        order.save()
                        return False
                
                # Validate position for sell orders
                if order.side == 'sell':
                    if not OrderExecutionService.validate_sell_position(order):
                        order.status = 'rejected'
                        order.notes = f"{order.notes}\nRejected: Insufficient shares to sell"
                        order.save()
                        return False
                
                # Execute at limit price
                trade = Trade.objects.create(
                    order=order,
                    symbol=order.symbol,
                    side=order.side,
                    quantity=order.quantity,
                    price=order.price,
                    execution_venue='SIMULATOR'
                )
                
                logger.info(f"Limit order {order.id} executed: {trade.quantity} shares at ${trade.price}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to execute limit order {order.id}: {str(e)}")
            return False
    
    @staticmethod
    def execute_stop_order(order: Order) -> bool:
        """
        Execute a stop order when stop price is triggered.
        """
        try:
            current_price = OrderExecutionService.get_current_price(order.symbol)
            if not current_price:
                return False
            
            # Check if stop order is triggered
            triggered = False
            if order.side == 'buy' and current_price >= order.stop_price:
                triggered = True
            elif order.side == 'sell' and current_price <= order.stop_price:
                triggered = True
            
            if not triggered:
                return False
            
            # Convert to market order and execute
            with transaction.atomic():
                # Apply slippage for stop orders (higher slippage due to urgency)
                slippage_factor = Decimal('0.002')  # 0.2%
                if order.side == 'buy':
                    execution_price = current_price * (Decimal('1') + slippage_factor)
                else:
                    execution_price = current_price * (Decimal('1') - slippage_factor)
                
                # Validate account balance for buy orders
                if order.side == 'buy':
                    total_cost = Decimal(str(order.quantity)) * execution_price + order.commission
                    if total_cost > order.account.current_balance:
                        order.status = 'rejected'
                        order.notes = f"{order.notes}\nRejected: Insufficient funds"
                        order.save()
                        return False
                
                # Validate position for sell orders
                if order.side == 'sell':
                    if not OrderExecutionService.validate_sell_position(order):
                        order.status = 'rejected'
                        order.notes = f"{order.notes}\nRejected: Insufficient shares to sell"
                        order.save()
                        return False
                
                # Create trade execution
                trade = Trade.objects.create(
                    order=order,
                    symbol=order.symbol,
                    side=order.side,
                    quantity=order.quantity,
                    price=execution_price,
                    execution_venue='SIMULATOR'
                )
                
                logger.info(f"Stop order {order.id} triggered and executed: {trade.quantity} shares at ${trade.price}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to execute stop order {order.id}: {str(e)}")
            return False
    
    @staticmethod
    def get_current_price(symbol: str) -> Optional[Decimal]:
        """
        Get current market price for a symbol.
        """
        try:
            symbol_obj = Symbol.objects.get(symbol=symbol, is_active=True)
            latest_data = MarketData.objects.filter(symbol=symbol_obj, is_latest=True).first()
            return latest_data.price if latest_data else None
        except Symbol.DoesNotExist:
            return None
    
    @staticmethod
    def validate_sell_position(order: Order) -> bool:
        """
        Validate that account has sufficient shares to sell.
        """
        from .models import Portfolio, Position
        
        try:
            portfolio = Portfolio.objects.get(account=order.account)
            position = Position.objects.filter(
                portfolio=portfolio,
                symbol=order.symbol
            ).first()
            
            if not position or position.quantity < order.quantity:
                return False
            
            return True
        except Portfolio.DoesNotExist:
            return False
    
    @staticmethod
    def process_pending_orders() -> Dict[str, int]:
        """
        Process all pending orders and execute those that meet conditions.
        """
        results = {
            'processed': 0,
            'executed': 0,
            'rejected': 0,
            'expired': 0
        }
        
        # Get all pending orders
        pending_orders = Order.objects.filter(
            status__in=['pending', 'partially_filled']
        ).select_related('account')
        
        for order in pending_orders:
            results['processed'] += 1
            
            # Check if order has expired
            if order.expires_at and order.expires_at <= timezone.now():
                order.status = 'expired'
                order.save()
                results['expired'] += 1
                continue
            
            # Try to execute based on order type
            executed = False
            if order.order_type == 'market':
                executed = OrderExecutionService.execute_market_order(order)
            elif order.order_type == 'limit':
                executed = OrderExecutionService.execute_limit_order(order)
            elif order.order_type == 'stop':
                executed = OrderExecutionService.execute_stop_order(order)
            elif order.order_type == 'stop_limit':
                # First check if stop is triggered, then execute as limit
                current_price = OrderExecutionService.get_current_price(order.symbol)
                if current_price:
                    triggered = False
                    if order.side == 'buy' and current_price >= order.stop_price:
                        triggered = True
                    elif order.side == 'sell' and current_price <= order.stop_price:
                        triggered = True
                    
                    if triggered:
                        executed = OrderExecutionService.execute_limit_order(order)
            
            if executed:
                results['executed'] += 1
            elif order.status == 'rejected':
                results['rejected'] += 1
        
        logger.info(f"Order processing complete: {results}")
        return results

    @staticmethod
    def execute_trailing_stop_order(order: Order) -> bool:
        """
        Execute a trailing stop order.
        """
        try:
            current_price = OrderExecutionService.get_current_price(order.symbol)
            if not current_price:
                return False

            # Update trailing stop price
            order.update_trailing_stop(current_price)

            # Check if stop is triggered
            triggered = False
            if order.side == 'sell' and order.stop_price and current_price <= order.stop_price:
                triggered = True
            elif order.side == 'buy' and order.stop_price and current_price >= order.stop_price:
                triggered = True

            if triggered:
                # Execute as market order
                return OrderExecutionService.execute_market_order(order)

            return False

        except Exception as e:
            logger.error(f"Failed to execute trailing stop order {order.id}: {str(e)}")
            return False

    @staticmethod
    def execute_conditional_order(order: Order) -> bool:
        """
        Execute a conditional order if condition is met.
        """
        try:
            # Check if condition is met
            if not order.check_condition():
                return False

            # Convert to appropriate order type and execute
            if order.price:
                # Execute as limit order
                return OrderExecutionService.execute_limit_order(order)
            else:
                # Execute as market order
                return OrderExecutionService.execute_market_order(order)

        except Exception as e:
            logger.error(f"Failed to execute conditional order {order.id}: {str(e)}")
            return False

    @staticmethod
    def create_bracket_order(account, symbol: str, quantity: int, entry_price: Decimal,
                           stop_loss: Decimal, take_profit: Decimal) -> Dict[str, any]:
        """
        Create a bracket order (entry + stop loss + take profit).
        """
        try:
            with transaction.atomic():
                # Create main entry order
                entry_order = Order.objects.create(
                    account=account,
                    symbol=symbol,
                    order_type='limit',
                    side='buy',
                    quantity=quantity,
                    price=entry_price,
                    time_in_force='gtc'
                )

                # Create stop loss order
                stop_order = Order.objects.create(
                    account=account,
                    symbol=symbol,
                    order_type='stop',
                    side='sell',
                    quantity=quantity,
                    stop_price=stop_loss,
                    parent_order=entry_order,
                    time_in_force='gtc'
                )

                # Create take profit order
                profit_order = Order.objects.create(
                    account=account,
                    symbol=symbol,
                    order_type='limit',
                    side='sell',
                    quantity=quantity,
                    price=take_profit,
                    parent_order=entry_order,
                    time_in_force='gtc'
                )

                return {
                    'success': True,
                    'entry_order': entry_order,
                    'stop_order': stop_order,
                    'profit_order': profit_order
                }

        except Exception as e:
            logger.error(f"Failed to create bracket order: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    @staticmethod
    def process_advanced_orders() -> Dict[str, int]:
        """
        Process advanced order types (trailing stops, conditionals, etc.).
        """
        results = {
            'processed': 0,
            'executed': 0,
            'updated': 0
        }

        # Process trailing stop orders
        trailing_orders = Order.objects.filter(
            status__in=['pending', 'partially_filled'],
            order_type='trailing_stop'
        )

        for order in trailing_orders:
            results['processed'] += 1

            current_price = OrderExecutionService.get_current_price(order.symbol)
            if current_price:
                # Update trailing stop
                if order.update_trailing_stop(current_price):
                    results['updated'] += 1

                # Check if triggered
                if OrderExecutionService.execute_trailing_stop_order(order):
                    results['executed'] += 1

        # Process conditional orders
        conditional_orders = Order.objects.filter(
            status__in=['pending', 'partially_filled'],
            order_type='conditional'
        )

        for order in conditional_orders:
            results['processed'] += 1

            if OrderExecutionService.execute_conditional_order(order):
                results['executed'] += 1

        return results


class TradingRiskService:
    """
    Service for trading risk management and validation.
    """
    
    @staticmethod
    def validate_order_risk(order: Order) -> Dict[str, any]:
        """
        Validate order against risk management rules.
        """
        warnings = []
        errors = []
        
        # Check position concentration
        if order.side == 'buy':
            estimated_value = Decimal(str(order.quantity)) * (order.price or Decimal('100'))
            account_equity = order.account.equity
            
            if account_equity > 0:
                concentration = (estimated_value / account_equity) * 100
                if concentration > 50:
                    warnings.append(f"High concentration risk: {concentration:.1f}% of portfolio")
                elif concentration > 25:
                    warnings.append(f"Moderate concentration risk: {concentration:.1f}% of portfolio")
        
        # Check order size vs average volume
        try:
            symbol_obj = Symbol.objects.get(symbol=order.symbol)
            latest_data = MarketData.objects.filter(symbol=symbol_obj, is_latest=True).first()
            if latest_data and latest_data.volume:
                volume_percentage = (order.quantity / latest_data.volume) * 100
                if volume_percentage > 10:
                    warnings.append(f"Large order size: {volume_percentage:.1f}% of daily volume")
        except Symbol.DoesNotExist:
            pass
        
        # Check account balance
        if order.side == 'buy':
            estimated_cost = order.estimated_cost
            if estimated_cost > order.account.current_balance:
                errors.append("Insufficient account balance")
        
        return {
            'is_valid': len(errors) == 0,
            'warnings': warnings,
            'errors': errors
        }

    @staticmethod
    def calculate_position_size(account, symbol_price: Decimal, risk_amount: Decimal,
                              stop_loss_price: Decimal = None) -> Dict[str, any]:
        """
        Calculate optimal position size based on risk management rules.
        """
        from .models import RiskManagement

        try:
            risk_mgmt = RiskManagement.objects.get(account=account)
        except RiskManagement.DoesNotExist:
            # Create default risk management settings
            risk_mgmt = RiskManagement.objects.create(account=account)

        # Calculate position size based on risk amount
        if stop_loss_price and symbol_price:
            risk_per_share = abs(symbol_price - stop_loss_price)
            if risk_per_share > 0:
                max_shares_by_risk = int(risk_amount / risk_per_share)
            else:
                max_shares_by_risk = 0
        else:
            max_shares_by_risk = risk_mgmt.calculate_position_size(symbol_price, risk_amount)

        # Calculate position size based on portfolio percentage
        portfolio_value = account.equity
        max_position_value = portfolio_value * (risk_mgmt.max_position_size_percent / Decimal('100'))

        if risk_mgmt.max_position_size_amount:
            max_position_value = min(max_position_value, risk_mgmt.max_position_size_amount)

        max_shares_by_position = int(max_position_value / symbol_price) if symbol_price > 0 else 0

        # Use the smaller of the two calculations
        recommended_shares = min(max_shares_by_risk, max_shares_by_position)

        return {
            'recommended_shares': recommended_shares,
            'max_shares_by_risk': max_shares_by_risk,
            'max_shares_by_position': max_shares_by_position,
            'max_position_value': max_position_value,
            'risk_per_share': risk_per_share if stop_loss_price else None
        }

    @staticmethod
    def calculate_risk_metrics(account) -> Dict[str, any]:
        """
        Calculate comprehensive risk metrics for an account.
        """
        from .models import RiskManagement, Portfolio, Position

        try:
            risk_mgmt = RiskManagement.objects.get(account=account)
        except RiskManagement.DoesNotExist:
            risk_mgmt = RiskManagement.objects.create(account=account)

        try:
            portfolio = Portfolio.objects.get(account=account)
            positions = Position.objects.filter(portfolio=portfolio, quantity__gt=0)
        except Portfolio.DoesNotExist:
            positions = Position.objects.none()

        # Calculate portfolio concentration
        total_value = account.equity
        concentration_risk = Decimal('0.00')
        largest_position_percent = Decimal('0.00')

        if total_value > 0:
            for position in positions:
                position_value = position.quantity * position.current_price
                position_percent = (position_value / total_value) * Decimal('100')

                if position_percent > largest_position_percent:
                    largest_position_percent = position_percent

                if position_percent > risk_mgmt.max_position_size_percent:
                    concentration_risk += position_percent - risk_mgmt.max_position_size_percent

        # Calculate current drawdown
        current_drawdown = risk_mgmt.calculate_current_drawdown()

        # Check daily loss limit
        daily_loss_limit_reached = risk_mgmt.check_daily_loss_limit()

        # Calculate leverage
        total_position_value = sum(
            position.quantity * position.current_price for position in positions
        )
        current_leverage = total_position_value / total_value if total_value > 0 else Decimal('0.00')

        # Risk score (0-100, higher is riskier)
        risk_score = Decimal('0.00')

        # Add points for concentration risk
        risk_score += min(concentration_risk * Decimal('2'), Decimal('30'))

        # Add points for drawdown
        risk_score += min(current_drawdown * Decimal('2'), Decimal('40'))

        # Add points for leverage
        if current_leverage > risk_mgmt.max_leverage:
            risk_score += min((current_leverage - risk_mgmt.max_leverage) * Decimal('20'), Decimal('30'))

        return {
            'risk_score': min(risk_score, Decimal('100')),
            'concentration_risk': concentration_risk,
            'largest_position_percent': largest_position_percent,
            'current_drawdown': current_drawdown,
            'daily_loss_limit_reached': daily_loss_limit_reached,
            'current_leverage': current_leverage,
            'max_leverage': risk_mgmt.max_leverage,
            'total_positions': positions.count(),
            'portfolio_value': total_value
        }

    @staticmethod
    def generate_risk_alerts(account) -> list:
        """
        Generate risk alerts based on current portfolio state.
        """
        alerts = []
        metrics = TradingRiskService.calculate_risk_metrics(account)

        # High concentration risk
        if metrics['concentration_risk'] > Decimal('10'):
            alerts.append({
                'type': 'warning',
                'title': 'High Concentration Risk',
                'message': f"Portfolio concentration risk is {metrics['concentration_risk']:.1f}% above limits"
            })

        # High drawdown
        if metrics['current_drawdown'] > Decimal('15'):
            alerts.append({
                'type': 'danger',
                'title': 'High Drawdown',
                'message': f"Current drawdown is {metrics['current_drawdown']:.1f}%"
            })

        # Daily loss limit
        if metrics['daily_loss_limit_reached']:
            alerts.append({
                'type': 'danger',
                'title': 'Daily Loss Limit Reached',
                'message': 'Daily loss limit has been reached. Consider stopping trading for today.'
            })

        # High leverage
        if metrics['current_leverage'] > metrics['max_leverage']:
            alerts.append({
                'type': 'warning',
                'title': 'Leverage Exceeded',
                'message': f"Current leverage ({metrics['current_leverage']:.2f}x) exceeds maximum ({metrics['max_leverage']:.2f}x)"
            })

        # High risk score
        if metrics['risk_score'] > Decimal('70'):
            alerts.append({
                'type': 'warning',
                'title': 'High Risk Score',
                'message': f"Portfolio risk score is {metrics['risk_score']:.0f}/100"
            })

        return alerts
