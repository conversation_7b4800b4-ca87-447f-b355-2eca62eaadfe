{% extends 'base.html' %}
{% load static %}

{% block title %}{{ quiz.title }} - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
.quiz-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 30px;
}

.quiz-info-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.attempt-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
}

.attempt-passed {
    border-left-color: #28a745;
    background-color: #d4edda;
}

.attempt-failed {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

.quiz-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.requirements-card {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.warning-card {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.success-card {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.best-score-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    display: inline-block;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Quiz Header -->
    <div class="quiz-header">
        <div class="row align-items-center">
            <div class="col-lg-8">
                {% if quiz.tutorial %}
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="{% url 'education:home' %}" class="text-white text-decoration-none">Education</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{% url 'education:learning_path_detail' quiz.tutorial.learning_path.id %}" 
                               class="text-white text-decoration-none">
                                {{ quiz.tutorial.learning_path.title }}
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{% url 'education:tutorial_detail' quiz.tutorial.id %}" 
                               class="text-white text-decoration-none">
                                {{ quiz.tutorial.title }}
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ quiz.title }}</li>
                    </ol>
                </nav>
                {% endif %}
                
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-question-circle me-3"></i>{{ quiz.title }}
                </h1>
                <p class="lead mb-0">{{ quiz.description }}</p>
            </div>
            <div class="col-lg-4 text-center">
                {% if best_attempt %}
                <div class="best-score-badge">
                    <i class="fas fa-trophy me-2"></i>Best Score: {{ best_attempt.percentage }}%
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Quiz Information -->
            <div class="quiz-info-card">
                <h4 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>Quiz Information
                </h4>
                
                <div class="quiz-stats">
                    <div class="stat-item">
                        <div class="stat-number">{{ quiz.total_questions }}</div>
                        <div class="stat-label">Questions</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ quiz.passing_score }}%</div>
                        <div class="stat-label">Passing Score</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ quiz.max_attempts }}</div>
                        <div class="stat-label">Max Attempts</div>
                    </div>
                    {% if quiz.time_limit %}
                    <div class="stat-item">
                        <div class="stat-number">{{ quiz.time_limit }}</div>
                        <div class="stat-label">Time Limit (min)</div>
                    </div>
                    {% endif %}
                </div>

                <!-- Requirements -->
                <div class="requirements-card">
                    <h5><i class="fas fa-clipboard-check me-2"></i>Requirements</h5>
                    <ul class="mb-0">
                        <li>Minimum score to pass: <strong>{{ quiz.passing_score }}%</strong></li>
                        <li>Maximum attempts allowed: <strong>{{ quiz.max_attempts }}</strong></li>
                        {% if quiz.time_limit %}
                        <li>Time limit: <strong>{{ quiz.time_limit }} minutes</strong></li>
                        {% endif %}
                        {% if quiz.show_correct_answers %}
                        <li>Correct answers will be shown after completion</li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Attempt Status -->
                {% if not can_take_quiz %}
                <div class="warning-card">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Maximum Attempts Reached</h5>
                    <p class="mb-0">You have used all {{ quiz.max_attempts }} attempts for this quiz. 
                    {% if best_attempt and best_attempt.is_passed %}
                    Congratulations on passing with {{ best_attempt.percentage }}%!
                    {% else %}
                    Please review the material and contact your instructor if you need additional attempts.
                    {% endif %}
                    </p>
                </div>
                {% elif best_attempt and best_attempt.is_passed %}
                <div class="success-card">
                    <h5><i class="fas fa-check-circle me-2"></i>Quiz Passed!</h5>
                    <p class="mb-0">You have successfully passed this quiz with a score of {{ best_attempt.percentage }}%. 
                    You can retake the quiz to improve your score if desired.</p>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="text-center mt-4">
                    {% if can_take_quiz %}
                    <a href="{% url 'education:take_quiz' quiz.id %}" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-play me-2"></i>
                        {% if attempts %}Take Quiz (Attempt {{ attempts.count|add:1 }}){% else %}Start Quiz{% endif %}
                    </a>
                    {% endif %}
                    
                    {% if quiz.tutorial %}
                    <a href="{% url 'education:tutorial_detail' quiz.tutorial.id %}" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Tutorial
                    </a>
                    {% else %}
                    <a href="{% url 'education:home' %}" class="btn btn-outline-primary">
                        <i class="fas fa-home me-2"></i>Education Home
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Previous Attempts -->
            {% if attempts %}
            <div class="quiz-info-card">
                <h5 class="mb-3">
                    <i class="fas fa-history me-2"></i>Your Attempts
                </h5>
                
                {% for attempt in attempts %}
                <div class="attempt-card {% if attempt.is_passed %}attempt-passed{% else %}attempt-failed{% endif %}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <strong>Attempt {{ attempt.attempt_number }}</strong>
                        <span class="badge {% if attempt.is_passed %}bg-success{% else %}bg-danger{% endif %}">
                            {{ attempt.percentage }}%
                        </span>
                    </div>
                    
                    <div class="small text-muted mb-2">
                        {{ attempt.completed_at|date:"M d, Y g:i A" }}
                        {% if attempt.time_taken %}
                        • {{ attempt.time_taken|floatformat:0 }}s
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="small">
                            {% if attempt.is_passed %}
                            <i class="fas fa-check text-success me-1"></i>Passed
                            {% else %}
                            <i class="fas fa-times text-danger me-1"></i>Failed
                            {% endif %}
                        </span>
                        {% if attempt.is_completed %}
                        <a href="{% url 'education:quiz_result' attempt.id %}" 
                           class="btn btn-sm btn-outline-primary">
                            View Results
                        </a>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Quiz Tips -->
            <div class="quiz-info-card">
                <h5 class="mb-3">
                    <i class="fas fa-lightbulb me-2"></i>Quiz Tips
                </h5>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Read each question carefully
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Review the tutorial material first
                    </li>
                    {% if quiz.time_limit %}
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Manage your time wisely
                    </li>
                    {% endif %}
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Don't rush - think through each answer
                    </li>
                    {% if quiz.randomize_questions %}
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Questions are randomized for each attempt
                    </li>
                    {% endif %}
                </ul>
            </div>

            <!-- Related Content -->
            {% if quiz.tutorial %}
            <div class="quiz-info-card">
                <h5 class="mb-3">
                    <i class="fas fa-book me-2"></i>Related Tutorial
                </h5>
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">{{ quiz.tutorial.title }}</h6>
                        <p class="card-text small text-muted">{{ quiz.tutorial.description|truncatewords:15 }}</p>
                        <a href="{% url 'education:tutorial_detail' quiz.tutorial.id %}" 
                           class="btn btn-sm btn-outline-primary">
                            Review Tutorial
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive features here
    console.log('Quiz detail page loaded');
});
</script>
{% endblock %}
