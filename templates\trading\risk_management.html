{% extends 'base.html' %}
{% load static %}

{% block title %}Risk Management - {{ account.name }} - Trading Simulator{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item">
    <a href="{% url 'trading:account_overview' %}" class="text-decoration-none">
        <i class="fas fa-wallet me-1"></i>Trading
    </a>
</li>
<li class="breadcrumb-item">
    <a href="{% url 'trading:account_detail' account.id %}" class="text-decoration-none">
        {{ account.name }}
    </a>
</li>
<li class="breadcrumb-item active" aria-current="page">
    Risk Management
</li>
{% endblock %}

{% block extra_css %}
<style>
    .risk-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    
    .risk-score-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 15px;
    }
    
    .alert-card {
        border-left: 4px solid #dc3545;
    }
    
    .metrics-card {
        border-left: 4px solid #28a745;
    }
    
    .risk-gauge {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: conic-gradient(
            #28a745 0deg,
            #28a745 72deg,
            #ffc107 72deg,
            #ffc107 144deg,
            #dc3545 144deg,
            #dc3545 360deg
        );
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }
    
    .risk-gauge::before {
        content: '';
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background: white;
        position: absolute;
    }
    
    .risk-score {
        position: relative;
        z-index: 1;
        font-weight: bold;
        color: #333;
        text-align: center;
    }
    
    .position-recommendation {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .metric-item {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 col-lg-2 dashboard-sidebar p-0">
            <div class="p-3">
                <div class="sidebar-section-title">Trading</div>
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="{% url 'trading:account_overview' %}" class="nav-link">
                            <i class="fas fa-wallet"></i>
                            Accounts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:order_entry' %}" class="nav-link">
                            <i class="fas fa-plus-circle"></i>
                            Place Order
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:bracket_order_entry' %}" class="nav-link">
                            <i class="fas fa-layer-group"></i>
                            Bracket Order
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:risk_management' %}" class="nav-link active">
                            <i class="fas fa-shield-alt"></i>
                            Risk Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:portfolio_overview' %}" class="nav-link">
                            <i class="fas fa-chart-pie"></i>
                            Portfolio
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 p-4">
            <!-- Risk Header -->
            <div class="risk-header p-4 mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1><i class="fas fa-shield-alt me-3"></i>Risk Management</h1>
                        <p class="mb-0">Account: {{ account.name }} • Portfolio Value: ${{ risk_metrics.portfolio_value|floatformat:2 }}</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="{% url 'trading:account_detail' account.id %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Account
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Risk Alerts -->
            {% if risk_alerts %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card alert-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>Risk Alerts
                            </h5>
                        </div>
                        <div class="card-body">
                            {% for alert in risk_alerts %}
                            <div class="alert alert-{{ alert.type }} mb-2">
                                <strong>{{ alert.title }}:</strong> {{ alert.message }}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <div class="row">
                <!-- Risk Score -->
                <div class="col-lg-4">
                    <div class="card risk-score-card mb-4">
                        <div class="card-body text-center">
                            <h6 class="card-title">
                                <i class="fas fa-tachometer-alt me-2"></i>Risk Score
                            </h6>
                            
                            <div class="risk-gauge mb-3">
                                <div class="risk-score">
                                    <div class="h3 mb-0">{{ risk_metrics.risk_score|floatformat:0 }}</div>
                                    <small>/100</small>
                                </div>
                            </div>
                            
                            <p class="mb-0">
                                {% if risk_metrics.risk_score < 30 %}
                                <span class="badge bg-success">Low Risk</span>
                                {% elif risk_metrics.risk_score < 70 %}
                                <span class="badge bg-warning">Moderate Risk</span>
                                {% else %}
                                <span class="badge bg-danger">High Risk</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <!-- Risk Metrics -->
                    <div class="card metrics-card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Risk Metrics
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="metric-item">
                                <div class="fw-bold">Current Drawdown</div>
                                <div class="h6 {% if risk_metrics.current_drawdown > 15 %}text-danger{% elif risk_metrics.current_drawdown > 10 %}text-warning{% else %}text-success{% endif %}">
                                    {{ risk_metrics.current_drawdown|floatformat:1 }}%
                                </div>
                            </div>
                            
                            <div class="metric-item">
                                <div class="fw-bold">Largest Position</div>
                                <div class="h6">{{ risk_metrics.largest_position_percent|floatformat:1 }}%</div>
                            </div>
                            
                            <div class="metric-item">
                                <div class="fw-bold">Current Leverage</div>
                                <div class="h6 {% if risk_metrics.current_leverage > risk_metrics.max_leverage %}text-danger{% else %}text-success{% endif %}">
                                    {{ risk_metrics.current_leverage|floatformat:2 }}x
                                </div>
                            </div>
                            
                            <div class="metric-item">
                                <div class="fw-bold">Total Positions</div>
                                <div class="h6">{{ risk_metrics.total_positions }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Position Sizing Recommendations -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator me-2"></i>Position Sizing Recommendations
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">
                                Based on 2% risk per trade and current portfolio value
                            </p>
                            
                            {% for rec in position_recommendations %}
                            <div class="position-recommendation">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="mb-1">{{ rec.symbol }}</h6>
                                        <small class="text-muted">${{ rec.current_price|floatformat:2 }}</small>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="fw-bold">{{ rec.recommended_shares }}</div>
                                        <small class="text-muted">Shares</small>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="fw-bold">${{ rec.max_position_value|floatformat:0 }}</div>
                                        <small class="text-muted">Max Position</small>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="fw-bold">${{ rec.risk_per_share|floatformat:2 }}</div>
                                        <small class="text-muted">Risk/Share</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Recent Trades -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>Recent Trades
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if recent_trades %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Symbol</th>
                                            <th>Side</th>
                                            <th class="text-end">Quantity</th>
                                            <th class="text-end">Price</th>
                                            <th class="text-end">Amount</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for trade in recent_trades %}
                                        <tr>
                                            <td><strong>{{ trade.symbol }}</strong></td>
                                            <td>
                                                <span class="badge {% if trade.side == 'buy' %}bg-success{% else %}bg-danger{% endif %}">
                                                    {{ trade.get_side_display|upper }}
                                                </span>
                                            </td>
                                            <td class="text-end">{{ trade.quantity }}</td>
                                            <td class="text-end">${{ trade.price|floatformat:2 }}</td>
                                            <td class="text-end">${{ trade.net_amount|floatformat:2 }}</td>
                                            <td>{{ trade.executed_at|date:"M d, H:i" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted text-center py-3">No recent trades</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Risk Management Settings -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>Risk Management Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="metric-item">
                                        <div class="fw-bold">Max Position Size</div>
                                        <div class="h6">{{ risk_mgmt.max_position_size_percent }}%</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="metric-item">
                                        <div class="fw-bold">Max Daily Loss</div>
                                        <div class="h6">{{ risk_mgmt.max_daily_loss_percent }}%</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="metric-item">
                                        <div class="fw-bold">Max Drawdown</div>
                                        <div class="h6">{{ risk_mgmt.max_drawdown_percent }}%</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="metric-item">
                                        <div class="fw-bold">Max Leverage</div>
                                        <div class="h6">{{ risk_mgmt.max_leverage }}x</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh risk metrics every 60 seconds
setInterval(() => {
    location.reload();
}, 60000);
</script>
{% endblock %}
