# Generated migration for performance optimization indexes

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('trading', '0004_order_condition_operator_order_condition_price_and_more'),
    ]

    operations = [
        # Account indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_accounts_user_status ON trading_accounts(user_id, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_accounts_user_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_accounts_status_updated ON trading_accounts(status, updated_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_accounts_status_updated;"
        ),
        
        # Order indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_orders_account_status ON trading_orders(account_id, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_orders_account_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_orders_symbol_status ON trading_orders(symbol, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_orders_symbol_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_orders_status_created ON trading_orders(status, created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_orders_status_created;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_orders_type_status ON trading_orders(order_type, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_orders_type_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_orders_parent_order ON trading_orders(parent_order_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_orders_parent_order;"
        ),
        
        # Trade indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_trades_order_executed ON trading_trades(order_id, executed_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_trades_order_executed;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_trades_symbol_executed ON trading_trades(symbol, executed_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_trades_symbol_executed;"
        ),
        
        # Portfolio indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_portfolios_account_updated ON trading_portfolios(account_id, updated_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_portfolios_account_updated;"
        ),
        
        # Position indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_positions_portfolio_symbol ON trading_positions(portfolio_id, symbol);",
            reverse_sql="DROP INDEX IF EXISTS idx_positions_portfolio_symbol;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_positions_symbol_quantity ON trading_positions(symbol, quantity);",
            reverse_sql="DROP INDEX IF EXISTS idx_positions_symbol_quantity;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_positions_portfolio_quantity ON trading_positions(portfolio_id, quantity);",
            reverse_sql="DROP INDEX IF EXISTS idx_positions_portfolio_quantity;"
        ),
        
        # Transaction indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_transactions_account_type ON trading_transactions(account_id, transaction_type);",
            reverse_sql="DROP INDEX IF EXISTS idx_transactions_account_type;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_transactions_account_created ON trading_transactions(account_id, created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_transactions_account_created;"
        ),
        
        # Portfolio History indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_portfolio_history_portfolio_date ON trading_portfolio_history(portfolio_id, date);",
            reverse_sql="DROP INDEX IF EXISTS idx_portfolio_history_portfolio_date;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_portfolio_history_date ON trading_portfolio_history(date);",
            reverse_sql="DROP INDEX IF EXISTS idx_portfolio_history_date;"
        ),
        
        # Risk Management indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_risk_management_account ON trading_risk_management(account_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_risk_management_account;"
        ),
    ]
