"""
Management command for cache operations.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.cache import caches
from apps.trading.cache import <PERSON><PERSON><PERSON>ana<PERSON>, CacheWarmer, MarketDataCache, PortfolioCache
from apps.trading.models import Account


class Command(BaseCommand):
    help = 'Manage cache operations for the trading application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear all cache entries',
        )
        parser.add_argument(
            '--clear-cache',
            type=str,
            choices=['default', 'market_data', 'sessions'],
            help='Clear specific cache',
        )
        parser.add_argument(
            '--warm',
            action='store_true',
            help='Warm cache with frequently accessed data',
        )
        parser.add_argument(
            '--warm-user',
            type=int,
            help='Warm cache for specific user ID',
        )
        parser.add_argument(
            '--warm-account',
            type=str,
            help='Warm cache for specific account ID',
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show cache statistics',
        )
        parser.add_argument(
            '--invalidate-user',
            type=int,
            help='Invalidate cache for specific user ID',
        )
        parser.add_argument(
            '--invalidate-account',
            type=str,
            help='Invalidate cache for specific account ID',
        )

    def handle(self, *args, **options):
        """Handle cache management operations."""
        self.stdout.write(f'Starting cache management at {timezone.now()}')
        
        if options['clear']:
            self.clear_all_cache()
        
        if options['clear_cache']:
            self.clear_specific_cache(options['clear_cache'])
        
        if options['stats']:
            self.show_cache_stats()
        
        if options['warm']:
            self.warm_cache()
        
        if options['warm_user']:
            self.warm_user_cache(options['warm_user'])
        
        if options['warm_account']:
            self.warm_account_cache(options['warm_account'])
        
        if options['invalidate_user']:
            self.invalidate_user_cache(options['invalidate_user'])
        
        if options['invalidate_account']:
            self.invalidate_account_cache(options['invalidate_account'])
        
        if not any(options.values()):
            self.stdout.write(
                self.style.WARNING('No operation specified. Use --help to see available options.')
            )
    
    def clear_all_cache(self):
        """Clear all cache entries."""
        self.stdout.write('Clearing all cache entries...')
        
        try:
            for cache_alias in ['default', 'market_data', 'sessions']:
                cache_instance = caches[cache_alias]
                cache_instance.clear()
                self.stdout.write(f'  Cleared {cache_alias} cache')
            
            self.stdout.write(
                self.style.SUCCESS('All cache entries cleared successfully.')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error clearing cache: {str(e)}')
            )
    
    def clear_specific_cache(self, cache_alias):
        """Clear specific cache."""
        self.stdout.write(f'Clearing {cache_alias} cache...')
        
        try:
            cache_instance = caches[cache_alias]
            cache_instance.clear()
            
            self.stdout.write(
                self.style.SUCCESS(f'{cache_alias} cache cleared successfully.')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error clearing {cache_alias} cache: {str(e)}')
            )
    
    def show_cache_stats(self):
        """Show cache statistics."""
        self.stdout.write('Gathering cache statistics...')
        
        try:
            for cache_alias in ['default', 'market_data', 'sessions']:
                cache_instance = caches[cache_alias]
                
                self.stdout.write(f'\n{cache_alias.upper()} Cache:')
                
                # Try to get cache info (Redis specific)
                if hasattr(cache_instance, '_cache'):
                    redis_client = cache_instance._cache.get_client()
                    if hasattr(redis_client, 'info'):
                        info = redis_client.info()
                        self.stdout.write(f'  Connected clients: {info.get("connected_clients", "N/A")}')
                        self.stdout.write(f'  Used memory: {info.get("used_memory_human", "N/A")}')
                        self.stdout.write(f'  Total commands processed: {info.get("total_commands_processed", "N/A")}')
                        self.stdout.write(f'  Keyspace hits: {info.get("keyspace_hits", "N/A")}')
                        self.stdout.write(f'  Keyspace misses: {info.get("keyspace_misses", "N/A")}')
                        
                        # Calculate hit ratio
                        hits = info.get("keyspace_hits", 0)
                        misses = info.get("keyspace_misses", 0)
                        if hits + misses > 0:
                            hit_ratio = (hits / (hits + misses)) * 100
                            self.stdout.write(f'  Hit ratio: {hit_ratio:.2f}%')
                    else:
                        self.stdout.write('  Cache statistics not available')
                else:
                    self.stdout.write('  Cache statistics not available')
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error gathering cache statistics: {str(e)}')
            )
    
    def warm_cache(self):
        """Warm cache with frequently accessed data."""
        self.stdout.write('Warming cache with frequently accessed data...')
        
        try:
            # Warm market data cache
            market_success = CacheWarmer.warm_market_data_cache()
            if market_success:
                self.stdout.write('  Market data cache warmed successfully')
            else:
                self.stdout.write('  Market data cache warming failed')
            
            # Warm cache for active accounts
            active_accounts = Account.objects.filter(status='active')[:10]  # Limit to first 10
            
            warmed_count = 0
            for account in active_accounts:
                user_success = CacheWarmer.warm_user_cache(account.user.id)
                if user_success:
                    warmed_count += 1
            
            self.stdout.write(f'  Cache warmed for {warmed_count} users')
            
            self.stdout.write(
                self.style.SUCCESS('Cache warming completed successfully.')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error warming cache: {str(e)}')
            )
    
    def warm_user_cache(self, user_id):
        """Warm cache for specific user."""
        self.stdout.write(f'Warming cache for user {user_id}...')
        
        try:
            success = CacheWarmer.warm_user_cache(user_id)
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f'Cache warmed successfully for user {user_id}.')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Cache warming completed with warnings for user {user_id}.')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error warming cache for user {user_id}: {str(e)}')
            )
    
    def warm_account_cache(self, account_id):
        """Warm cache for specific account."""
        self.stdout.write(f'Warming cache for account {account_id}...')
        
        try:
            # Get portfolio summary (this will cache it)
            PortfolioCache.get_portfolio_summary(account_id)
            
            # Get trading statistics (this will cache it)
            PortfolioCache.get_trading_statistics(account_id)
            
            self.stdout.write(
                self.style.SUCCESS(f'Cache warmed successfully for account {account_id}.')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error warming cache for account {account_id}: {str(e)}')
            )
    
    def invalidate_user_cache(self, user_id):
        """Invalidate cache for specific user."""
        self.stdout.write(f'Invalidating cache for user {user_id}...')
        
        try:
            deleted_count = CacheManager.invalidate_user_cache(user_id)
            
            self.stdout.write(
                self.style.SUCCESS(f'Invalidated {deleted_count} cache entries for user {user_id}.')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error invalidating cache for user {user_id}: {str(e)}')
            )
    
    def invalidate_account_cache(self, account_id):
        """Invalidate cache for specific account."""
        self.stdout.write(f'Invalidating cache for account {account_id}...')
        
        try:
            deleted_count = CacheManager.invalidate_account_cache(account_id)
            
            self.stdout.write(
                self.style.SUCCESS(f'Invalidated {deleted_count} cache entries for account {account_id}.')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error invalidating cache for account {account_id}: {str(e)}')
            )
