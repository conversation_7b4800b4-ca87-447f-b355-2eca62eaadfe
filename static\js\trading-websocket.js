/**
 * Trading WebSocket Client
 * Handles real-time updates for portfolio, orders, risk, and prices
 */

class TradingWebSocket {
    constructor(accountId) {
        this.accountId = accountId;
        this.isInitialized = false;
        this.updateCallbacks = new Map();
        this.priceSubscriptions = new Set();
        
        // Bind methods
        this.handlePortfolioUpdate = this.handlePortfolioUpdate.bind(this);
        this.handleOrderUpdate = this.handleOrderUpdate.bind(this);
        this.handleRiskUpdate = this.handleRiskUpdate.bind(this);
        this.handlePriceUpdate = this.handlePriceUpdate.bind(this);
    }
    
    /**
     * Initialize WebSocket connections
     */
    initialize() {
        if (this.isInitialized) {
            console.warn('Trading WebSocket already initialized');
            return;
        }
        
        console.log(`Initializing Trading WebSocket for account: ${this.accountId}`);
        
        // Connect to portfolio updates
        window.wsManager.connect('portfolio', `/ws/trading/portfolio/${this.accountId}/`, {
            onMessage: this.handlePortfolioUpdate,
            onOpen: () => {
                console.log('Portfolio WebSocket connected');
                this.requestPortfolioUpdate();
            },
            onClose: () => console.log('Portfolio WebSocket disconnected'),
            onError: (error) => console.error('Portfolio WebSocket error:', error)
        });
        
        // Connect to order updates
        window.wsManager.connect('orders', `/ws/trading/orders/${this.accountId}/`, {
            onMessage: this.handleOrderUpdate,
            onOpen: () => {
                console.log('Orders WebSocket connected');
                this.requestOrderUpdate();
            },
            onClose: () => console.log('Orders WebSocket disconnected'),
            onError: (error) => console.error('Orders WebSocket error:', error)
        });
        
        // Connect to risk updates
        window.wsManager.connect('risk', `/ws/trading/risk/${this.accountId}/`, {
            onMessage: this.handleRiskUpdate,
            onOpen: () => {
                console.log('Risk WebSocket connected');
                this.requestRiskUpdate();
            },
            onClose: () => console.log('Risk WebSocket disconnected'),
            onError: (error) => console.error('Risk WebSocket error:', error)
        });
        
        // Connect to price updates
        window.wsManager.connect('prices', '/ws/trading/prices/', {
            onMessage: this.handlePriceUpdate,
            onOpen: () => {
                console.log('Prices WebSocket connected');
                this.subscribeToCurrentPrices();
            },
            onClose: () => console.log('Prices WebSocket disconnected'),
            onError: (error) => console.error('Prices WebSocket error:', error)
        });
        
        this.isInitialized = true;
        
        // Set up periodic updates
        this.setupPeriodicUpdates();
    }
    
    /**
     * Disconnect all WebSocket connections
     */
    disconnect() {
        window.wsManager.disconnect('portfolio');
        window.wsManager.disconnect('orders');
        window.wsManager.disconnect('risk');
        window.wsManager.disconnect('prices');
        this.isInitialized = false;
        
        // Clear periodic updates
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
    
    /**
     * Register callback for specific update types
     */
    onUpdate(type, callback) {
        if (!this.updateCallbacks.has(type)) {
            this.updateCallbacks.set(type, []);
        }
        this.updateCallbacks.get(type).push(callback);
    }
    
    /**
     * Trigger callbacks for update type
     */
    triggerCallbacks(type, data) {
        const callbacks = this.updateCallbacks.get(type);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in ${type} callback:`, error);
                }
            });
        }
    }
    
    /**
     * Handle portfolio updates
     */
    handlePortfolioUpdate(data) {
        if (data.type === 'portfolio_update') {
            console.log('Portfolio update received:', data);
            this.updatePortfolioDisplay(data.data);
            this.triggerCallbacks('portfolio', data.data);
        }
    }
    
    /**
     * Handle order updates
     */
    handleOrderUpdate(data) {
        if (data.type === 'orders_update') {
            console.log('Orders update received:', data);
            this.updateOrdersDisplay(data.data);
            this.triggerCallbacks('orders', data.data);
        } else if (data.type === 'order_update') {
            console.log('Single order update received:', data);
            this.updateSingleOrder(data.data);
            this.triggerCallbacks('order', data.data);
        }
    }
    
    /**
     * Handle risk updates
     */
    handleRiskUpdate(data) {
        if (data.type === 'risk_update') {
            console.log('Risk update received:', data);
            this.updateRiskDisplay(data.data);
            this.triggerCallbacks('risk', data.data);
        }
    }
    
    /**
     * Handle price updates
     */
    handlePriceUpdate(data) {
        if (data.type === 'price_update') {
            console.log('Price update received:', data);
            this.updatePriceDisplay(data.price_data);
            this.triggerCallbacks('price', data.price_data);
        }
    }
    
    /**
     * Request portfolio update
     */
    requestPortfolioUpdate() {
        window.wsManager.send('portfolio', { type: 'request_update' });
    }
    
    /**
     * Request order update
     */
    requestOrderUpdate() {
        window.wsManager.send('orders', { type: 'request_update' });
    }
    
    /**
     * Request risk update
     */
    requestRiskUpdate() {
        window.wsManager.send('risk', { type: 'request_update' });
    }
    
    /**
     * Subscribe to price updates for symbols
     */
    subscribeToPrices(symbols) {
        symbols.forEach(symbol => this.priceSubscriptions.add(symbol));
        window.wsManager.send('prices', { 
            type: 'subscribe', 
            symbols: Array.from(this.priceSubscriptions) 
        });
    }
    
    /**
     * Subscribe to prices for symbols currently in portfolio
     */
    subscribeToCurrentPrices() {
        const symbols = this.getCurrentPortfolioSymbols();
        if (symbols.length > 0) {
            this.subscribeToPrices(symbols);
        }
    }
    
    /**
     * Get symbols from current portfolio
     */
    getCurrentPortfolioSymbols() {
        const symbols = [];
        
        // Get symbols from portfolio table
        const portfolioTable = document.querySelector('#portfolio-positions tbody');
        if (portfolioTable) {
            portfolioTable.querySelectorAll('tr').forEach(row => {
                const symbolCell = row.querySelector('td:first-child strong');
                if (symbolCell) {
                    symbols.push(symbolCell.textContent.trim());
                }
            });
        }
        
        // Get symbols from order forms
        const symbolInputs = document.querySelectorAll('input[name="symbol"], input[id*="symbol"]');
        symbolInputs.forEach(input => {
            if (input.value) {
                symbols.push(input.value.toUpperCase());
            }
        });
        
        return [...new Set(symbols)]; // Remove duplicates
    }
    
    /**
     * Update portfolio display
     */
    updatePortfolioDisplay(data) {
        if (!data) return;
        
        // Update account balance
        const balanceElements = document.querySelectorAll('.account-balance, .cash-balance');
        balanceElements.forEach(el => {
            if (data.cash_balance !== undefined) {
                el.textContent = `$${data.cash_balance.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
            }
        });
        
        // Update total equity
        const equityElements = document.querySelectorAll('.total-equity, .portfolio-value');
        equityElements.forEach(el => {
            if (data.total_equity !== undefined) {
                el.textContent = `$${data.total_equity.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
            }
        });
        
        // Update day change
        const changeElements = document.querySelectorAll('.day-change');
        changeElements.forEach(el => {
            if (data.day_change !== undefined) {
                const changeClass = data.day_change >= 0 ? 'text-success' : 'text-danger';
                const changeSign = data.day_change >= 0 ? '+' : '';
                el.className = `day-change ${changeClass}`;
                el.textContent = `${changeSign}$${data.day_change.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})} (${data.day_change_percent?.toFixed(2) || '0.00'}%)`;
            }
        });
        
        // Update positions table
        this.updatePositionsTable(data.positions);
    }
    
    /**
     * Update positions table
     */
    updatePositionsTable(positions) {
        const tbody = document.querySelector('#portfolio-positions tbody');
        if (!tbody || !positions) return;
        
        // Clear existing rows
        tbody.innerHTML = '';
        
        if (positions.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No positions</td></tr>';
            return;
        }
        
        // Add position rows
        positions.forEach(position => {
            const row = document.createElement('tr');
            const pnlClass = position.unrealized_pnl >= 0 ? 'text-success' : 'text-danger';
            const pnlSign = position.unrealized_pnl >= 0 ? '+' : '';
            
            row.innerHTML = `
                <td><strong>${position.symbol}</strong></td>
                <td class="text-end">${position.quantity}</td>
                <td class="text-end">$${position.average_price.toFixed(2)}</td>
                <td class="text-end">$${position.current_price.toFixed(2)}</td>
                <td class="text-end">$${position.market_value.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                <td class="text-end ${pnlClass}">
                    ${pnlSign}$${Math.abs(position.unrealized_pnl).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    <br><small>(${pnlSign}${position.unrealized_pnl_percent.toFixed(2)}%)</small>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="openOrderForm('${position.symbol}', 'sell')">
                        <i class="fas fa-minus"></i> Sell
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }
    
    /**
     * Update orders display
     */
    updateOrdersDisplay(data) {
        // Implementation for updating orders table
        console.log('Updating orders display:', data);
    }
    
    /**
     * Update single order
     */
    updateSingleOrder(data) {
        // Implementation for updating single order
        console.log('Updating single order:', data);
    }
    
    /**
     * Update risk display
     */
    updateRiskDisplay(data) {
        // Implementation for updating risk metrics
        console.log('Updating risk display:', data);
    }
    
    /**
     * Update price display
     */
    updatePriceDisplay(data) {
        // Update price displays throughout the page
        const priceElements = document.querySelectorAll(`[data-symbol="${data.symbol}"] .current-price, .price-${data.symbol}`);
        priceElements.forEach(el => {
            el.textContent = `$${data.price.toFixed(2)}`;
            
            // Add price change animation
            el.classList.add('price-updated');
            setTimeout(() => el.classList.remove('price-updated'), 1000);
        });
    }
    
    /**
     * Setup periodic updates
     */
    setupPeriodicUpdates() {
        // Request updates every 30 seconds
        this.updateInterval = setInterval(() => {
            if (this.isInitialized) {
                this.requestPortfolioUpdate();
                this.requestRiskUpdate();
            }
        }, 30000);
    }
}

// Global trading WebSocket instance
window.tradingWS = null;

/**
 * Initialize trading WebSocket for account
 */
function initializeTradingWebSocket(accountId) {
    if (window.tradingWS) {
        window.tradingWS.disconnect();
    }
    
    window.tradingWS = new TradingWebSocket(accountId);
    window.tradingWS.initialize();
    
    return window.tradingWS;
}

/**
 * Disconnect trading WebSocket
 */
function disconnectTradingWebSocket() {
    if (window.tradingWS) {
        window.tradingWS.disconnect();
        window.tradingWS = null;
    }
}
