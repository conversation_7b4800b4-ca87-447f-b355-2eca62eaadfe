"""
Caching utilities and strategies for trading application.
"""

import logging
import hashlib
from functools import wraps
from django.core.cache import cache, caches
from django.core.cache.utils import make_template_fragment_key
from django.utils import timezone
from django.conf import settings
from decimal import Decimal
import json

logger = logging.getLogger(__name__)


class CacheManager:
    """Centralized cache management for trading operations."""
    
    # Cache aliases
    DEFAULT_CACHE = 'default'
    MARKET_DATA_CACHE = 'market_data'
    SESSION_CACHE = 'sessions'
    
    # Cache timeouts (in seconds)
    TIMEOUTS = {
        'portfolio': 30,           # Portfolio data changes frequently
        'market_data': 15,         # Market data is very dynamic
        'user_profile': 300,       # User profile changes less frequently
        'trading_stats': 120,      # Trading statistics
        'risk_metrics': 60,        # Risk calculations
        'order_history': 180,      # Order history
        'symbols': 3600,           # Symbol data is relatively static
        'exchange_data': 7200,     # Exchange data is very static
    }
    
    @classmethod
    def get_cache(cls, alias=None):
        """Get cache instance by alias."""
        if alias is None:
            alias = cls.DEFAULT_CACHE
        return caches[alias]
    
    @classmethod
    def generate_key(cls, prefix, *args, **kwargs):
        """Generate a consistent cache key."""
        # Convert all arguments to strings
        key_parts = [str(prefix)]
        key_parts.extend(str(arg) for arg in args)
        
        # Add sorted kwargs
        for key, value in sorted(kwargs.items()):
            key_parts.append(f"{key}:{value}")
        
        # Create hash for very long keys
        key_string = ":".join(key_parts)
        if len(key_string) > 200:  # Redis key length limit
            key_hash = hashlib.md5(key_string.encode()).hexdigest()
            return f"{prefix}:hash:{key_hash}"
        
        return key_string
    
    @classmethod
    def set(cls, key, value, timeout=None, cache_alias=None):
        """Set cache value with optional timeout and cache selection."""
        cache_instance = cls.get_cache(cache_alias)
        
        if timeout is None:
            timeout = cls.TIMEOUTS.get('default', 300)
        
        try:
            cache_instance.set(key, value, timeout)
            logger.debug(f"Cache set: {key} (timeout: {timeout}s)")
            return True
        except Exception as e:
            logger.error(f"Cache set failed for key {key}: {str(e)}")
            return False
    
    @classmethod
    def get(cls, key, default=None, cache_alias=None):
        """Get cache value with optional default and cache selection."""
        cache_instance = cls.get_cache(cache_alias)
        
        try:
            value = cache_instance.get(key, default)
            if value is not default:
                logger.debug(f"Cache hit: {key}")
            else:
                logger.debug(f"Cache miss: {key}")
            return value
        except Exception as e:
            logger.error(f"Cache get failed for key {key}: {str(e)}")
            return default
    
    @classmethod
    def delete(cls, key, cache_alias=None):
        """Delete cache value."""
        cache_instance = cls.get_cache(cache_alias)
        
        try:
            cache_instance.delete(key)
            logger.debug(f"Cache deleted: {key}")
            return True
        except Exception as e:
            logger.error(f"Cache delete failed for key {key}: {str(e)}")
            return False
    
    @classmethod
    def delete_pattern(cls, pattern, cache_alias=None):
        """Delete cache keys matching pattern."""
        cache_instance = cls.get_cache(cache_alias)
        
        try:
            if hasattr(cache_instance, 'delete_pattern'):
                deleted_count = cache_instance.delete_pattern(pattern)
                logger.debug(f"Cache pattern deleted: {pattern} ({deleted_count} keys)")
                return deleted_count
            else:
                logger.warning(f"Cache backend doesn't support pattern deletion: {pattern}")
                return 0
        except Exception as e:
            logger.error(f"Cache pattern delete failed for pattern {pattern}: {str(e)}")
            return 0
    
    @classmethod
    def invalidate_user_cache(cls, user_id):
        """Invalidate all cache entries for a specific user."""
        patterns = [
            f"portfolio_data_{user_id}_*",
            f"order_history_{user_id}_*",
            f"trading_stats_{user_id}_*",
            f"risk_metrics_{user_id}_*",
            f"portfolio_performance_{user_id}_*",
        ]
        
        total_deleted = 0
        for pattern in patterns:
            total_deleted += cls.delete_pattern(pattern)
        
        logger.info(f"Invalidated {total_deleted} cache entries for user {user_id}")
        return total_deleted
    
    @classmethod
    def invalidate_account_cache(cls, account_id):
        """Invalidate all cache entries for a specific account."""
        patterns = [
            f"portfolio_data_{account_id}*",
            f"order_history_{account_id}*",
            f"trading_stats_{account_id}*",
            f"risk_metrics_{account_id}*",
            f"portfolio_performance_{account_id}*",
        ]
        
        total_deleted = 0
        for pattern in patterns:
            total_deleted += cls.delete_pattern(pattern)
        
        logger.info(f"Invalidated {total_deleted} cache entries for account {account_id}")
        return total_deleted


def cache_result(timeout=None, cache_alias=None, key_prefix=None):
    """Decorator to cache function results."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_prefix:
                prefix = key_prefix
            else:
                prefix = f"{func.__module__}.{func.__name__}"
            
            cache_key = CacheManager.generate_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = CacheManager.get(cache_key, cache_alias=cache_alias)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            
            # Determine timeout
            func_timeout = timeout
            if func_timeout is None:
                func_timeout = CacheManager.TIMEOUTS.get(key_prefix, 300)
            
            CacheManager.set(cache_key, result, func_timeout, cache_alias)
            return result
        
        return wrapper
    return decorator


class MarketDataCache:
    """Specialized caching for market data."""
    
    @staticmethod
    def get_latest_prices(symbols):
        """Get latest prices for symbols with caching."""
        cache_key = CacheManager.generate_key('latest_prices', *sorted(symbols))
        cached_prices = CacheManager.get(cache_key, cache_alias='market_data')
        
        if cached_prices:
            return cached_prices
        
        # Fetch from database
        from apps.market_data.models import MarketData, Symbol
        
        prices = {}
        market_data = MarketData.objects.filter(
            symbol__symbol__in=symbols,
            is_latest=True
        ).select_related('symbol')
        
        for md in market_data:
            prices[md.symbol.symbol] = {
                'price': float(md.price),
                'change': float(md.change),
                'change_percent': float(md.change_percent),
                'volume': int(md.volume),
                'timestamp': md.timestamp.isoformat()
            }
        
        # Cache for 15 seconds
        CacheManager.set(cache_key, prices, 15, 'market_data')
        return prices
    
    @staticmethod
    def invalidate_symbol_cache(symbol):
        """Invalidate cache for a specific symbol."""
        patterns = [
            f"latest_prices*{symbol}*",
            f"market_data*{symbol}*",
            f"symbol_info_{symbol}*",
        ]
        
        total_deleted = 0
        for pattern in patterns:
            total_deleted += CacheManager.delete_pattern(pattern, 'market_data')
        
        return total_deleted


class PortfolioCache:
    """Specialized caching for portfolio data."""
    
    @staticmethod
    @cache_result(timeout=30, key_prefix='portfolio_summary')
    def get_portfolio_summary(account_id):
        """Get cached portfolio summary."""
        from .optimization import QueryOptimizer
        return QueryOptimizer.get_optimized_portfolio_data(account_id)
    
    @staticmethod
    @cache_result(timeout=120, key_prefix='trading_stats')
    def get_trading_statistics(account_id):
        """Get cached trading statistics."""
        from .optimization import QueryOptimizer
        return QueryOptimizer.get_trading_statistics(account_id)
    
    @staticmethod
    def invalidate_portfolio_cache(account_id):
        """Invalidate portfolio cache for an account."""
        return CacheManager.invalidate_account_cache(account_id)


class TemplateFragmentCache:
    """Utilities for template fragment caching."""
    
    @staticmethod
    def invalidate_fragment(fragment_name, *args):
        """Invalidate a template fragment cache."""
        cache_key = make_template_fragment_key(fragment_name, args)
        return CacheManager.delete(cache_key)
    
    @staticmethod
    def invalidate_user_fragments(user_id):
        """Invalidate template fragments for a user."""
        fragments = [
            ('portfolio_overview', user_id),
            ('order_list', user_id),
            ('trading_dashboard', user_id),
            ('risk_dashboard', user_id),
        ]
        
        deleted_count = 0
        for fragment_name, *args in fragments:
            if TemplateFragmentCache.invalidate_fragment(fragment_name, *args):
                deleted_count += 1
        
        return deleted_count


class CacheWarmer:
    """Utilities to pre-warm cache with frequently accessed data."""
    
    @staticmethod
    def warm_user_cache(user_id):
        """Pre-warm cache for a user's frequently accessed data."""
        from .models import Account
        
        try:
            # Get user's accounts
            accounts = Account.objects.filter(user_id=user_id, status='active')
            
            for account in accounts:
                # Warm portfolio cache
                PortfolioCache.get_portfolio_summary(account.id)
                
                # Warm trading statistics cache
                PortfolioCache.get_trading_statistics(account.id)
                
                logger.info(f"Cache warmed for account {account.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Cache warming failed for user {user_id}: {str(e)}")
            return False
    
    @staticmethod
    def warm_market_data_cache():
        """Pre-warm cache for popular market data."""
        popular_symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'META', 'NVDA', 'SPY', 'QQQ']
        
        try:
            # Warm latest prices cache
            MarketDataCache.get_latest_prices(popular_symbols)
            
            logger.info(f"Market data cache warmed for {len(popular_symbols)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"Market data cache warming failed: {str(e)}")
            return False


# Cache invalidation signals
def invalidate_cache_on_model_change(sender, instance, **kwargs):
    """Signal handler to invalidate cache when models change."""
    model_name = sender.__name__.lower()
    
    if model_name == 'order':
        CacheManager.invalidate_account_cache(instance.account.id)
    elif model_name == 'trade':
        CacheManager.invalidate_account_cache(instance.order.account.id)
    elif model_name == 'portfolio':
        CacheManager.invalidate_account_cache(instance.account.id)
    elif model_name == 'position':
        CacheManager.invalidate_account_cache(instance.portfolio.account.id)
    elif model_name == 'marketdata':
        MarketDataCache.invalidate_symbol_cache(instance.symbol.symbol)
