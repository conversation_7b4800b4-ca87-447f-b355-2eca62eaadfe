"""
URL configuration for education app.
"""

from django.urls import path
from . import views

app_name = 'education'

urlpatterns = [
    # Main education pages
    path('', views.education_home, name='home'),
    path('learning-paths/', views.learning_paths, name='learning_paths'),
    path('learning-paths/<uuid:path_id>/', views.learning_path_detail, name='learning_path_detail'),
    path('learning-paths/<uuid:path_id>/start/', views.start_learning_path, name='start_learning_path'),
    
    # Tutorial pages
    path('tutorials/<uuid:tutorial_id>/', views.tutorial_detail, name='tutorial_detail'),
    path('tutorial-steps/<uuid:step_id>/complete/', views.complete_tutorial_step, name='complete_tutorial_step'),
    
    # Quiz pages
    path('quizzes/<uuid:quiz_id>/', views.quiz_detail, name='quiz_detail'),
    path('quizzes/<uuid:quiz_id>/take/', views.take_quiz, name='take_quiz'),
    path('quiz-results/<uuid:attempt_id>/', views.quiz_result, name='quiz_result'),
    
    # User progress
    path('my-progress/', views.user_progress_dashboard, name='progress_dashboard'),
]
