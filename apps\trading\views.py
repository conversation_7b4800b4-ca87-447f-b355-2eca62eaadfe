from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from decimal import Decimal
import logging

from .models import Account, Transaction, Portfolio, Position, PortfolioHistory, Order, Trade, RiskManagement
from .forms import VirtualDepositForm, AccountCreationForm, AccountUpdateForm, TransactionFilterForm, OrderForm, QuickOrderForm, BracketOrderForm
from .services import TradingRiskService
from apps.market_data.models import Symbol, MarketData
from apps.accounts.models import Notification

# Performance monitoring imports
from .optimization import DatabaseOptimizer, PerformanceMonitor, QueryOptimizer
from .cache import CacheManager, MarketDataCache, PortfolioCache

logger = logging.getLogger(__name__)


@login_required
def account_overview(request):
    """
    Main account overview page showing all user accounts.
    """
    accounts = Account.objects.filter(user=request.user).order_by('-created_at')

    # Calculate summary statistics
    total_balance = accounts.aggregate(
        total=Sum('current_balance')
    )['total'] or Decimal('0.00')

    total_equity = accounts.aggregate(
        total=Sum('equity')
    )['total'] or Decimal('0.00')

    context = {
        'accounts': accounts,
        'total_balance': total_balance,
        'total_equity': total_equity,
        'account_count': accounts.count(),
        'title': 'Account Overview'
    }

    return render(request, 'trading/account_overview.html', context)


@login_required
def account_detail(request, account_id):
    """
    Detailed view of a specific trading account.
    """
    account = get_object_or_404(Account, id=account_id, user=request.user)

    # Get recent transactions
    transactions = account.transactions.all()[:10]

    # Get transaction summary for the last 30 days
    thirty_days_ago = timezone.now() - timezone.timedelta(days=30)
    recent_transactions = account.transactions.filter(
        created_at__gte=thirty_days_ago,
        status='completed'
    )

    deposits_30d = recent_transactions.filter(
        transaction_type='deposit'
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

    withdrawals_30d = recent_transactions.filter(
        transaction_type='withdrawal'
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

    context = {
        'account': account,
        'transactions': transactions,
        'deposits_30d': deposits_30d,
        'withdrawals_30d': withdrawals_30d,
        'title': f'{account.name} - Account Details'
    }

    return render(request, 'trading/account_detail.html', context)


@login_required
def virtual_deposit(request):
    """
    Handle virtual deposits to trading accounts.
    """
    if request.method == 'POST':
        form = VirtualDepositForm(user=request.user, data=request.POST)
        if form.is_valid():
            account = form.cleaned_data['account']
            amount = form.cleaned_data['amount']
            description = form.cleaned_data.get('description', '')

            try:
                # Process the deposit
                account.update_balance(
                    amount=amount,
                    transaction_type='deposit',
                    description=description or f'Virtual deposit of ${amount:,.2f}'
                )

                # Create notification
                Notification.objects.create(
                    user=request.user,
                    title='Deposit Successful',
                    message=f'${amount:,.2f} has been deposited to your {account.name} account.',
                    notification_type='success'
                )

                messages.success(
                    request,
                    f'Successfully deposited ${amount:,.2f} to {account.name}. '
                    f'New balance: ${account.current_balance:,.2f}'
                )

                logger.info(f"User {request.user.username} deposited ${amount} to account {account.id}")

                return redirect('trading:account_detail', account_id=account.id)

            except Exception as e:
                logger.error(f"Deposit failed for user {request.user.username}: {str(e)}")
                messages.error(request, 'Deposit failed. Please try again.')

    else:
        form = VirtualDepositForm(user=request.user)

    context = {
        'form': form,
        'title': 'Virtual Deposit'
    }

    return render(request, 'trading/virtual_deposit.html', context)


@login_required
def create_account(request):
    """
    Create a new trading account.
    """
    if request.method == 'POST':
        form = AccountCreationForm(user=request.user, data=request.POST)
        if form.is_valid():
            account = form.save()

            # Create notification
            Notification.objects.create(
                user=request.user,
                title='Account Created',
                message=f'Your new {account.get_account_type_display()} "{account.name}" has been created successfully.',
                notification_type='success'
            )

            messages.success(
                request,
                f'Account "{account.name}" created successfully with initial balance of ${account.initial_balance:,.2f}'
            )

            logger.info(f"User {request.user.username} created account {account.id}")

            return redirect('trading:account_detail', account_id=account.id)
    else:
        form = AccountCreationForm(user=request.user)

    context = {
        'form': form,
        'title': 'Create New Account'
    }

    return render(request, 'trading/create_account.html', context)


@login_required
def transaction_history(request, account_id):
    """
    Display transaction history for a specific account.
    """
    account = get_object_or_404(Account, id=account_id, user=request.user)

    # Get all transactions for the account
    transactions = account.transactions.all()

    # Apply filters if provided
    filter_form = TransactionFilterForm(request.GET)
    if filter_form.is_valid():
        transaction_type = filter_form.cleaned_data.get('transaction_type')
        date_from = filter_form.cleaned_data.get('date_from')
        date_to = filter_form.cleaned_data.get('date_to')

        if transaction_type:
            transactions = transactions.filter(transaction_type=transaction_type)

        if date_from:
            transactions = transactions.filter(created_at__date__gte=date_from)

        if date_to:
            transactions = transactions.filter(created_at__date__lte=date_to)

    # Paginate results
    paginator = Paginator(transactions, 25)  # Show 25 transactions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'account': account,
        'page_obj': page_obj,
        'filter_form': filter_form,
        'title': f'{account.name} - Transaction History'
    }

    return render(request, 'trading/transaction_history.html', context)


@login_required
def update_account(request, account_id):
    """
    Update account details.
    """
    account = get_object_or_404(Account, id=account_id, user=request.user)

    if request.method == 'POST':
        form = AccountUpdateForm(user=request.user, data=request.POST, instance=account)
        if form.is_valid():
            form.save()

            messages.success(request, f'Account "{account.name}" updated successfully.')
            logger.info(f"User {request.user.username} updated account {account.id}")

            return redirect('trading:account_detail', account_id=account.id)
    else:
        form = AccountUpdateForm(user=request.user, instance=account)

    context = {
        'form': form,
        'account': account,
        'title': f'Update {account.name}'
    }

    return render(request, 'trading/update_account.html', context)


@login_required
@require_http_methods(["POST"])
def close_account(request, account_id):
    """
    Close a trading account (AJAX endpoint).
    """
    account = get_object_or_404(Account, id=account_id, user=request.user)

    if account.current_balance > 0:
        return JsonResponse({
            'success': False,
            'message': 'Cannot close account with remaining balance. Please withdraw all funds first.'
        })

    account.status = 'closed'
    account.save()

    # Create notification
    Notification.objects.create(
        user=request.user,
        title='Account Closed',
        message=f'Your account "{account.name}" has been closed.',
        notification_type='info'
    )

    logger.info(f"User {request.user.username} closed account {account.id}")

    return JsonResponse({
        'success': True,
        'message': f'Account "{account.name}" has been closed successfully.'
    })


@login_required
def deposit_history(request):
    """
    Display deposit history across all accounts.
    """
    # Get all deposit transactions for the user's accounts
    user_accounts = Account.objects.filter(user=request.user)
    deposits = Transaction.objects.filter(
        account__in=user_accounts,
        transaction_type='deposit',
        status='completed'
    ).select_related('account').order_by('-created_at')

    # Apply search filter
    search_query = request.GET.get('search', '')
    if search_query:
        deposits = deposits.filter(
            Q(description__icontains=search_query) |
            Q(account__name__icontains=search_query)
        )

    # Paginate results
    paginator = Paginator(deposits, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate summary statistics
    total_deposits = deposits.aggregate(
        total=Sum('amount')
    )['total'] or Decimal('0.00')

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_deposits': total_deposits,
        'title': 'Deposit History'
    }

    return render(request, 'trading/deposit_history.html', context)


@login_required
def portfolio_overview(request):
    """
    Display portfolio overview across all user accounts.
    """
    # Get all user accounts with portfolios
    accounts = Account.objects.filter(user=request.user, status='active')
    portfolios = []

    total_portfolio_value = Decimal('0.00')
    total_cash_value = Decimal('0.00')
    total_positions_value = Decimal('0.00')
    total_unrealized_pnl = Decimal('0.00')

    for account in accounts:
        # Get or create portfolio for each account
        portfolio, created = Portfolio.objects.get_or_create(account=account)

        if created:
            # Initialize new portfolio
            portfolio.calculate_portfolio_value()

        portfolios.append(portfolio)

        # Aggregate totals
        total_portfolio_value += portfolio.total_portfolio_value
        total_cash_value += portfolio.cash_value
        total_positions_value += portfolio.total_value
        total_unrealized_pnl += portfolio.unrealized_pnl

    # Get recent portfolio performance data
    performance_data = []
    if portfolios:
        # Get performance data for the first portfolio (or aggregate later)
        main_portfolio = portfolios[0] if portfolios else None
        if main_portfolio:
            performance_data = PortfolioHistory.get_performance_data(main_portfolio, days=30)

    context = {
        'portfolios': portfolios,
        'total_portfolio_value': total_portfolio_value,
        'total_cash_value': total_cash_value,
        'total_positions_value': total_positions_value,
        'total_unrealized_pnl': total_unrealized_pnl,
        'performance_data': performance_data,
        'title': 'Portfolio Overview'
    }

    return render(request, 'trading/portfolio_overview.html', context)


@login_required
def portfolio_detail(request, account_id):
    """
    Display detailed portfolio view for a specific account.
    """
    account = get_object_or_404(Account, id=account_id, user=request.user)

    # Get or create portfolio
    portfolio, created = Portfolio.objects.get_or_create(account=account)

    if created:
        portfolio.calculate_portfolio_value()

    # Get all positions
    positions = portfolio.positions.filter(quantity__gt=0).order_by('-current_value')

    # Get portfolio allocation data
    allocation_data = portfolio.get_allocation_data()

    # Get performance history
    performance_data = PortfolioHistory.get_performance_data(portfolio, days=30)

    # Create portfolio snapshot
    PortfolioHistory.create_snapshot(portfolio)

    context = {
        'account': account,
        'portfolio': portfolio,
        'positions': positions,
        'allocation_data': allocation_data,
        'performance_data': performance_data,
        'title': f'{account.name} - Portfolio'
    }

    return render(request, 'trading/portfolio_detail.html', context)


@login_required
def position_detail(request, position_id):
    """
    Display detailed view of a specific position.
    """
    position = get_object_or_404(
        Position,
        id=position_id,
        portfolio__account__user=request.user
    )

    # Get related transactions for this position (would need to add symbol to Transaction model)
    # For now, we'll show basic position information

    context = {
        'position': position,
        'portfolio': position.portfolio,
        'account': position.portfolio.account,
        'title': f'{position.symbol} - Position Details'
    }

    return render(request, 'trading/position_detail.html', context)


@login_required
def portfolio_performance(request, account_id):
    """
    Display portfolio performance analytics.
    """
    account = get_object_or_404(Account, id=account_id, user=request.user)
    portfolio = get_object_or_404(Portfolio, account=account)

    # Get performance data for different time periods
    performance_30d = PortfolioHistory.get_performance_data(portfolio, days=30)
    performance_90d = PortfolioHistory.get_performance_data(portfolio, days=90)
    performance_1y = PortfolioHistory.get_performance_data(portfolio, days=365)

    # Calculate performance metrics
    metrics = {
        'total_return': portfolio.total_return,
        'unrealized_pnl': portfolio.unrealized_pnl,
        'realized_pnl': portfolio.realized_pnl,
        'day_change': portfolio.day_change,
        'day_change_percent': portfolio.day_change_percent,
    }

    context = {
        'account': account,
        'portfolio': portfolio,
        'performance_30d': performance_30d,
        'performance_90d': performance_90d,
        'performance_1y': performance_1y,
        'metrics': metrics,
        'title': f'{account.name} - Performance Analytics'
    }

    return render(request, 'trading/portfolio_performance.html', context)


# Order Management Views

@login_required
def order_entry(request, account_id=None, symbol=None):
    """
    Order entry form for placing trades.
    """
    # Get account
    if account_id:
        account = get_object_or_404(Account, id=account_id, user=request.user, status='active')
    else:
        # Get user's first active account
        account = Account.objects.filter(user=request.user, status='active').first()
        if not account:
            messages.error(request, 'You need an active trading account to place orders.')
            return redirect('trading:create_account')

    # Get symbol object if provided
    symbol_obj = None
    if symbol:
        try:
            symbol_obj = Symbol.objects.get(symbol=symbol.upper(), is_active=True)
        except Symbol.DoesNotExist:
            messages.error(request, f'Symbol {symbol} not found.')
            return redirect('market_data:symbol_list')

    if request.method == 'POST':
        form = OrderForm(data=request.POST, account=account, symbol_obj=symbol_obj)
        if form.is_valid():
            order = form.save()

            # Create notification
            Notification.objects.create(
                user=request.user,
                title='Order Placed',
                message=f'{order.get_side_display()} order for {order.quantity} shares of {order.symbol} has been placed.',
                notification_type='success'
            )

            messages.success(
                request,
                f'Order placed successfully! {order.get_side_display()} {order.quantity} shares of {order.symbol} '
                f'at {order.get_order_type_display()} price.'
            )

            logger.info(f"User {request.user.username} placed order {order.id}")

            return redirect('trading:order_detail', order_id=order.id)
    else:
        form = OrderForm(account=account, symbol_obj=symbol_obj)

    # Get current market data for the symbol
    current_price = None
    if symbol_obj:
        latest_data = MarketData.objects.filter(symbol=symbol_obj, is_latest=True).first()
        current_price = latest_data.price if latest_data else None

    context = {
        'form': form,
        'account': account,
        'symbol_obj': symbol_obj,
        'current_price': current_price,
        'title': f'Place Order - {account.name}'
    }

    return render(request, 'trading/order_entry.html', context)


@login_required
def order_list(request, account_id=None):
    """
    Display list of orders for an account or all accounts.
    """
    if account_id:
        account = get_object_or_404(Account, id=account_id, user=request.user)
        orders = Order.objects.filter(account=account)
        title = f'{account.name} - Orders'
    else:
        account = None
        user_accounts = Account.objects.filter(user=request.user)
        orders = Order.objects.filter(account__in=user_accounts)
        title = 'All Orders'

    # Apply filters
    status_filter = request.GET.get('status', '')
    symbol_filter = request.GET.get('symbol', '')
    order_type_filter = request.GET.get('order_type', '')

    if status_filter:
        orders = orders.filter(status=status_filter)

    if symbol_filter:
        orders = orders.filter(symbol__icontains=symbol_filter)

    if order_type_filter:
        orders = orders.filter(order_type=order_type_filter)

    # Order by most recent first
    orders = orders.select_related('account').order_by('-created_at')

    # Paginate
    paginator = Paginator(orders, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'account': account,
        'page_obj': page_obj,
        'status_filter': status_filter,
        'symbol_filter': symbol_filter,
        'order_type_filter': order_type_filter,
        'order_status_choices': Order.ORDER_STATUS_CHOICES,
        'order_type_choices': Order.ORDER_TYPE_CHOICES,
        'title': title
    }

    return render(request, 'trading/order_list.html', context)


@login_required
def order_detail(request, order_id):
    """
    Display detailed view of a specific order.
    """
    order = get_object_or_404(Order, id=order_id, account__user=request.user)

    # Get related trades
    trades = order.trades.all().order_by('-executed_at')

    # Get current market price for comparison
    current_price = None
    try:
        symbol_obj = Symbol.objects.get(symbol=order.symbol)
        latest_data = MarketData.objects.filter(symbol=symbol_obj, is_latest=True).first()
        current_price = latest_data.price if latest_data else None
    except Symbol.DoesNotExist:
        pass

    context = {
        'order': order,
        'trades': trades,
        'current_price': current_price,
        'title': f'Order #{str(order.id)[:8]}... - {order.symbol}'
    }

    return render(request, 'trading/order_detail.html', context)


@login_required
@require_http_methods(["POST"])
def cancel_order(request, order_id):
    """
    Cancel an order (AJAX endpoint).
    """
    order = get_object_or_404(Order, id=order_id, account__user=request.user)

    if not order.can_cancel():
        return JsonResponse({
            'success': False,
            'message': f'Cannot cancel order in {order.get_status_display()} status'
        })

    try:
        order.cancel_order("User cancelled")

        # Create notification
        Notification.objects.create(
            user=request.user,
            title='Order Cancelled',
            message=f'Your {order.get_side_display()} order for {order.quantity} shares of {order.symbol} has been cancelled.',
            notification_type='info'
        )

        logger.info(f"User {request.user.username} cancelled order {order.id}")

        return JsonResponse({
            'success': True,
            'message': 'Order cancelled successfully'
        })

    except Exception as e:
        logger.error(f"Failed to cancel order {order.id}: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'Failed to cancel order. Please try again.'
        })


@login_required
def quick_order(request):
    """
    Quick order entry for market orders.
    """
    if request.method == 'POST':
        # Get user's first active account
        account = Account.objects.filter(user=request.user, status='active').first()
        if not account:
            return JsonResponse({
                'success': False,
                'message': 'No active trading account found'
            })

        form = QuickOrderForm(data=request.POST, account=account)
        if form.is_valid():
            order = form.create_order()
            if order:
                return JsonResponse({
                    'success': True,
                    'message': f'Market order placed for {order.quantity} shares of {order.symbol}',
                    'order_id': str(order.id)
                })

        # Return form errors
        errors = []
        for field, field_errors in form.errors.items():
            for error in field_errors:
                errors.append(f"{field}: {error}")

        return JsonResponse({
            'success': False,
            'message': 'Order validation failed',
            'errors': errors
        })

    return JsonResponse({
        'success': False,
        'message': 'Invalid request method'
    })


@login_required
def trade_history(request, account_id=None):
    """
    Display trade execution history.
    """
    if account_id:
        account = get_object_or_404(Account, id=account_id, user=request.user)
        trades = Trade.objects.filter(order__account=account)
        title = f'{account.name} - Trade History'
    else:
        account = None
        user_accounts = Account.objects.filter(user=request.user)
        trades = Trade.objects.filter(order__account__in=user_accounts)
        title = 'All Trades'

    # Apply filters
    symbol_filter = request.GET.get('symbol', '')
    side_filter = request.GET.get('side', '')

    if symbol_filter:
        trades = trades.filter(symbol__icontains=symbol_filter)

    if side_filter:
        trades = trades.filter(side=side_filter)

    # Order by most recent first
    trades = trades.select_related('order', 'order__account').order_by('-executed_at')

    # Paginate
    paginator = Paginator(trades, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate summary statistics
    total_trades = trades.count()
    total_volume = trades.aggregate(
        total=Sum('gross_amount')
    )['total'] or Decimal('0.00')

    context = {
        'account': account,
        'page_obj': page_obj,
        'symbol_filter': symbol_filter,
        'side_filter': side_filter,
        'total_trades': total_trades,
        'total_volume': total_volume,
        'side_choices': Order.ORDER_SIDE_CHOICES,
        'title': title
    }

    return render(request, 'trading/trade_history.html', context)


@login_required
def risk_management_dashboard(request, account_id=None):
    """
    Display risk management dashboard with metrics and alerts.
    """
    if account_id:
        account = get_object_or_404(Account, id=account_id, user=request.user)
    else:
        # Get user's first active account
        account = Account.objects.filter(user=request.user, status='active').first()
        if not account:
            messages.error(request, 'You need an active trading account to view risk management.')
            return redirect('trading:create_account')

    # Get or create risk management settings
    risk_mgmt, _ = RiskManagement.objects.get_or_create(account=account)

    # Calculate risk metrics
    risk_metrics = TradingRiskService.calculate_risk_metrics(account)

    # Generate risk alerts
    risk_alerts = TradingRiskService.generate_risk_alerts(account)

    # Get recent high-risk trades
    recent_trades = Trade.objects.filter(
        order__account=account
    ).select_related('order').order_by('-executed_at')[:10]

    # Calculate position sizing recommendations for popular symbols
    popular_symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA']
    position_recommendations = []

    for symbol in popular_symbols:
        try:
            symbol_obj = Symbol.objects.get(symbol=symbol)
            latest_data = MarketData.objects.filter(symbol=symbol_obj, is_latest=True).first()
            if latest_data:
                # Calculate position size for 2% risk
                risk_amount = account.equity * Decimal('0.02')  # 2% risk
                stop_loss_price = latest_data.price * Decimal('0.95')  # 5% stop loss

                position_calc = TradingRiskService.calculate_position_size(
                    account, latest_data.price, risk_amount, stop_loss_price
                )

                position_recommendations.append({
                    'symbol': symbol,
                    'current_price': latest_data.price,
                    'recommended_shares': position_calc['recommended_shares'],
                    'max_position_value': position_calc['max_position_value'],
                    'risk_per_share': position_calc.get('risk_per_share', 0)
                })
        except Symbol.DoesNotExist:
            continue

    context = {
        'account': account,
        'risk_mgmt': risk_mgmt,
        'risk_metrics': risk_metrics,
        'risk_alerts': risk_alerts,
        'recent_trades': recent_trades,
        'position_recommendations': position_recommendations,
        'title': f'Risk Management - {account.name}'
    }

    return render(request, 'trading/risk_management.html', context)


@login_required
def bracket_order_entry(request, account_id=None):
    """
    Bracket order entry form.
    """
    # Get account
    if account_id:
        account = get_object_or_404(Account, id=account_id, user=request.user, status='active')
    else:
        # Get user's first active account
        account = Account.objects.filter(user=request.user, status='active').first()
        if not account:
            messages.error(request, 'You need an active trading account to place bracket orders.')
            return redirect('trading:create_account')

    if request.method == 'POST':
        form = BracketOrderForm(data=request.POST, account=account)
        if form.is_valid():
            result = form.create_bracket_order()

            if result and result.get('success'):
                # Create notification
                Notification.objects.create(
                    user=request.user,
                    title='Bracket Order Created',
                    message=f'Bracket order for {form.cleaned_data["quantity"]} shares of {form.cleaned_data["symbol"]} has been created.',
                    notification_type='success'
                )

                messages.success(
                    request,
                    f'Bracket order created successfully! Entry, stop loss, and take profit orders have been placed.'
                )

                logger.info(f"User {request.user.username} created bracket order for {form.cleaned_data['symbol']}")

                return redirect('trading:order_list_account', account_id=account.id)
            else:
                messages.error(request, f"Failed to create bracket order: {result.get('error', 'Unknown error')}")
    else:
        form = BracketOrderForm(account=account)

    context = {
        'form': form,
        'account': account,
        'title': f'Bracket Order - {account.name}'
    }

    return render(request, 'trading/bracket_order_entry.html', context)


# Performance monitoring views (staff only)

def is_staff_user(user):
    """Check if user is staff."""
    return user.is_staff


@login_required
@user_passes_test(is_staff_user)
def performance_dashboard(request):
    """Performance monitoring dashboard."""
    from django.contrib.auth.decorators import user_passes_test
    from django.core.cache import cache

    context = {
        'title': 'Performance Dashboard',
        'current_time': timezone.now(),
    }

    try:
        # Get database metrics
        db_metrics = PerformanceMonitor.get_database_metrics()
        context['db_metrics'] = db_metrics

        # Get cache statistics
        cache_stats = get_cache_statistics()
        context['cache_stats'] = cache_stats

        # Get query performance analysis
        query_analysis = DatabaseOptimizer.analyze_query_performance()
        context['query_analysis'] = query_analysis

    except Exception as e:
        logger.error(f"Error loading performance dashboard: {str(e)}")
        context['error'] = str(e)

    return render(request, 'trading/performance/dashboard.html', context)


@login_required
@user_passes_test(is_staff_user)
def database_optimization_api(request):
    """API endpoint for database optimization operations."""
    from django.contrib.auth.decorators import user_passes_test

    if request.method == 'POST':
        operation = request.POST.get('operation')

        try:
            if operation == 'analyze':
                result = DatabaseOptimizer.analyze_query_performance()
                return JsonResponse({
                    'success': True,
                    'data': result,
                    'message': 'Database analysis completed'
                })

            elif operation == 'optimize':
                success = DatabaseOptimizer.optimize_database()
                return JsonResponse({
                    'success': success,
                    'message': 'Database optimization completed' if success else 'Optimization failed'
                })

            elif operation == 'cleanup':
                days_to_keep = int(request.POST.get('days_to_keep', 90))
                cleanup_stats = PerformanceMonitor.cleanup_old_data(days_to_keep)
                return JsonResponse({
                    'success': True,
                    'data': cleanup_stats,
                    'message': f'Data cleanup completed, kept {days_to_keep} days of data'
                })

            elif operation == 'metrics':
                metrics = PerformanceMonitor.get_database_metrics()
                return JsonResponse({
                    'success': True,
                    'data': metrics,
                    'message': 'Database metrics retrieved'
                })

            else:
                return JsonResponse({
                    'success': False,
                    'message': 'Invalid operation'
                }, status=400)

        except Exception as e:
            logger.error(f"Database optimization API error: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=500)

    return JsonResponse({
        'success': False,
        'message': 'Method not allowed'
    }, status=405)


@login_required
@user_passes_test(is_staff_user)
def cache_management_api(request):
    """API endpoint for cache management operations."""
    from django.contrib.auth.decorators import user_passes_test
    from django.core.cache import cache

    if request.method == 'POST':
        operation = request.POST.get('operation')

        try:
            if operation == 'clear_all':
                cache.clear()
                return JsonResponse({
                    'success': True,
                    'message': 'All cache cleared'
                })

            elif operation == 'clear_user':
                user_id = request.POST.get('user_id')
                if user_id:
                    deleted_count = CacheManager.invalidate_user_cache(user_id)
                    return JsonResponse({
                        'success': True,
                        'data': {'deleted_count': deleted_count},
                        'message': f'User cache cleared for user {user_id}'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'User ID required'
                    }, status=400)

            elif operation == 'clear_account':
                account_id = request.POST.get('account_id')
                if account_id:
                    deleted_count = CacheManager.invalidate_account_cache(account_id)
                    return JsonResponse({
                        'success': True,
                        'data': {'deleted_count': deleted_count},
                        'message': f'Account cache cleared for account {account_id}'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'Account ID required'
                    }, status=400)

            elif operation == 'warm_cache':
                # Warm cache for popular data
                success = warm_popular_cache()
                return JsonResponse({
                    'success': success,
                    'message': 'Cache warming completed' if success else 'Cache warming failed'
                })

            elif operation == 'stats':
                stats = get_cache_statistics()
                return JsonResponse({
                    'success': True,
                    'data': stats,
                    'message': 'Cache statistics retrieved'
                })

            else:
                return JsonResponse({
                    'success': False,
                    'message': 'Invalid operation'
                }, status=400)

        except Exception as e:
            logger.error(f"Cache management API error: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=500)

    return JsonResponse({
        'success': False,
        'message': 'Method not allowed'
    }, status=405)


@login_required
@user_passes_test(is_staff_user)
def query_performance_api(request):
    """API endpoint for query performance monitoring."""
    if request.method == 'GET':
        try:
            # Get slow query information
            slow_queries = get_slow_query_info()

            # Get query optimization suggestions
            optimization_suggestions = get_optimization_suggestions()

            return JsonResponse({
                'success': True,
                'data': {
                    'slow_queries': slow_queries,
                    'optimization_suggestions': optimization_suggestions,
                    'timestamp': timezone.now().isoformat()
                },
                'message': 'Query performance data retrieved'
            })

        except Exception as e:
            logger.error(f"Query performance API error: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=500)

    return JsonResponse({
        'success': False,
        'message': 'Method not allowed'
    }, status=405)


def get_cache_statistics():
    """Get cache statistics."""
    from django.core.cache import cache

    stats = {
        'default': {'status': 'unknown'},
        'market_data': {'status': 'unknown'},
        'sessions': {'status': 'unknown'}
    }

    try:
        # Test default cache
        test_key = 'cache_test'
        cache.set(test_key, 'test_value', 10)
        if cache.get(test_key) == 'test_value':
            stats['default']['status'] = 'working'
            cache.delete(test_key)
        else:
            stats['default']['status'] = 'error'

        # Get cache backend info
        stats['default']['backend'] = cache.__class__.__name__

    except Exception as e:
        stats['default']['status'] = 'error'
        stats['default']['error'] = str(e)

    return stats


def warm_popular_cache():
    """Warm cache with popular data."""
    try:
        # Warm market data cache
        popular_symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA']
        MarketDataCache.get_latest_prices(popular_symbols)

        # Warm portfolio cache for active accounts
        active_accounts = Account.objects.filter(status='active')[:5]

        for account in active_accounts:
            PortfolioCache.get_portfolio_summary(account.id)
            PortfolioCache.get_trading_statistics(account.id)

        return True

    except Exception as e:
        logger.error(f"Cache warming error: {str(e)}")
        return False


def get_slow_query_info():
    """Get information about slow queries."""
    # This is a placeholder - in production you'd integrate with query monitoring tools
    return [
        {
            'query': 'SELECT * FROM trading_orders WHERE status = ?',
            'avg_duration_ms': 150,
            'count': 25,
            'suggestion': 'Add index on status column'
        },
        {
            'query': 'SELECT * FROM market_data WHERE symbol_id = ? AND is_latest = ?',
            'avg_duration_ms': 80,
            'count': 100,
            'suggestion': 'Composite index already exists'
        }
    ]


def get_optimization_suggestions():
    """Get query optimization suggestions."""
    from django.db import connection

    suggestions = []

    with connection.cursor() as cursor:
        # Check for tables without indexes
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            AND name NOT LIKE 'django_%'
            AND name NOT LIKE 'auth_%'
            AND name NOT LIKE 'cache_%'
        """)

        tables = [row[0] for row in cursor.fetchall()]

        for table in tables:
            try:
                cursor.execute(f"PRAGMA index_list({table})")
                indexes = cursor.fetchall()

                if len(indexes) < 2:  # Only primary key
                    suggestions.append({
                        'type': 'missing_indexes',
                        'table': table,
                        'suggestion': f'Consider adding indexes to {table} table',
                        'priority': 'medium'
                    })
            except Exception:
                continue

    return suggestions
