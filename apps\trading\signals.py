"""
Django signals for real-time trading updates.
"""

import logging
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from .models import Order, Trade, Portfolio, Position
from apps.market_data.models import MarketData

logger = logging.getLogger(__name__)
channel_layer = get_channel_layer()


def send_portfolio_update(account_id):
    """Send portfolio update to WebSocket group."""
    if channel_layer:
        async_to_sync(channel_layer.group_send)(
            f'portfolio_{account_id}',
            {
                'type': 'portfolio_update',
                'data': {
                    'type': 'portfolio_update',
                    'account_id': str(account_id),
                    'message': 'Portfolio updated'
                }
            }
        )


def send_order_update(account_id, order_data=None):
    """Send order update to WebSocket group."""
    if channel_layer:
        async_to_sync(channel_layer.group_send)(
            f'orders_{account_id}',
            {
                'type': 'order_update',
                'data': {
                    'type': 'order_update',
                    'account_id': str(account_id),
                    'order_data': order_data,
                    'message': 'Order updated'
                }
            }
        )


def send_risk_update(account_id):
    """Send risk update to WebSocket group."""
    if channel_layer:
        async_to_sync(channel_layer.group_send)(
            f'risk_{account_id}',
            {
                'type': 'risk_update',
                'data': {
                    'type': 'risk_update',
                    'account_id': str(account_id),
                    'message': 'Risk metrics updated'
                }
            }
        )


def send_price_update(symbol, price_data):
    """Send price update to WebSocket group."""
    if channel_layer:
        async_to_sync(channel_layer.group_send)(
            'market_prices',
            {
                'type': 'price_update',
                'data': {
                    'type': 'price_update',
                    'symbol': symbol,
                    'price_data': price_data,
                    'message': 'Price updated'
                }
            }
        )


@receiver(post_save, sender=Order)
def order_saved(sender, instance, created, **kwargs):
    """Handle order save events."""
    try:
        account_id = instance.account.id
        
        order_data = {
            'id': str(instance.id),
            'symbol': instance.symbol,
            'order_type': instance.order_type,
            'side': instance.side,
            'quantity': int(instance.quantity),
            'filled_quantity': int(instance.filled_quantity),
            'price': float(instance.price) if instance.price else None,
            'status': instance.status,
            'created_at': instance.created_at.isoformat(),
            'updated_at': instance.updated_at.isoformat(),
            'is_new': created
        }
        
        # Send order update
        send_order_update(account_id, order_data)
        
        # Send risk update if order affects risk metrics
        if instance.status in ['filled', 'partially_filled', 'cancelled']:
            send_risk_update(account_id)
        
        logger.info(f"Order WebSocket update sent for order {instance.id}")
        
    except Exception as e:
        logger.error(f"Error sending order WebSocket update: {str(e)}")


@receiver(post_save, sender=Trade)
def trade_saved(sender, instance, created, **kwargs):
    """Handle trade save events."""
    try:
        account_id = instance.order.account.id
        
        # Send portfolio update when trade is executed
        send_portfolio_update(account_id)
        
        # Send order update to reflect filled quantity
        send_order_update(account_id)
        
        # Send risk update as trade affects portfolio
        send_risk_update(account_id)
        
        logger.info(f"Trade WebSocket updates sent for trade {instance.id}")
        
    except Exception as e:
        logger.error(f"Error sending trade WebSocket updates: {str(e)}")


@receiver(post_save, sender=Portfolio)
def portfolio_saved(sender, instance, **kwargs):
    """Handle portfolio save events."""
    try:
        account_id = instance.account.id
        
        # Send portfolio update
        send_portfolio_update(account_id)
        
        logger.info(f"Portfolio WebSocket update sent for account {account_id}")
        
    except Exception as e:
        logger.error(f"Error sending portfolio WebSocket update: {str(e)}")


@receiver(post_save, sender=Position)
@receiver(post_delete, sender=Position)
def position_changed(sender, instance, **kwargs):
    """Handle position change events."""
    try:
        account_id = instance.portfolio.account.id
        
        # Send portfolio update when positions change
        send_portfolio_update(account_id)
        
        # Send risk update as position changes affect risk metrics
        send_risk_update(account_id)
        
        logger.info(f"Position change WebSocket updates sent for account {account_id}")
        
    except Exception as e:
        logger.error(f"Error sending position change WebSocket updates: {str(e)}")


@receiver(post_save, sender=MarketData)
def market_data_saved(sender, instance, **kwargs):
    """Handle market data save events."""
    try:
        if instance.is_latest:
            price_data = {
                'symbol': instance.symbol.symbol,
                'price': float(instance.price),
                'change': float(instance.change),
                'change_percent': float(instance.change_percent),
                'volume': int(instance.volume),
                'timestamp': instance.timestamp.isoformat()
            }
            
            # Send price update
            send_price_update(instance.symbol.symbol, price_data)
            
            logger.debug(f"Price WebSocket update sent for {instance.symbol.symbol}")
        
    except Exception as e:
        logger.error(f"Error sending price WebSocket update: {str(e)}")


# Real-time notification functions for manual triggering
def notify_portfolio_update(account_id):
    """Manually trigger portfolio update notification."""
    send_portfolio_update(account_id)


def notify_order_update(account_id, order_data=None):
    """Manually trigger order update notification."""
    send_order_update(account_id, order_data)


def notify_risk_update(account_id):
    """Manually trigger risk update notification."""
    send_risk_update(account_id)


def notify_price_update(symbol, price_data):
    """Manually trigger price update notification."""
    send_price_update(symbol, price_data)
