{% extends 'base.html' %}
{% load static %}

{% block title %}{{ tutorial.title }} - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
.tutorial-header {
    background: linear-gradient(135deg, {{ tutorial.learning_path.category.color }}22 0%, {{ tutorial.learning_path.category.color }}44 100%);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.tutorial-content {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.tutorial-sidebar {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    position: sticky;
    top: 20px;
}

.step-item {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    border-left: 4px solid #e9ecef;
    transition: all 0.3s ease;
    cursor: pointer;
}

.step-item:hover {
    background-color: #f8f9fa;
}

.step-completed {
    background-color: #d4edda;
    border-left-color: #28a745;
}

.step-current {
    background-color: #fff3cd;
    border-left-color: #ffc107;
}

.step-locked {
    opacity: 0.6;
    cursor: not-allowed;
}

.progress-bar-custom {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    transition: width 0.5s ease;
}

.tutorial-navigation {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    bottom: 20px;
    z-index: 100;
}

.content-section {
    line-height: 1.8;
}

.content-section h1,
.content-section h2,
.content-section h3 {
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.content-section h1 {
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.content-section h2 {
    border-left: 4px solid #3498db;
    padding-left: 1rem;
}

.content-section code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

.content-section pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
}

.quiz-section {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 10px;
    padding: 20px;
    margin-top: 30px;
}

.achievement-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Tutorial Header -->
            <div class="tutorial-header">
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="{% url 'education:home' %}" class="text-decoration-none">Education</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{% url 'education:learning_path_detail' tutorial.learning_path.id %}" class="text-decoration-none">
                                {{ tutorial.learning_path.title }}
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">{{ tutorial.title }}</li>
                    </ol>
                </nav>
                
                <h1 class="display-6 fw-bold mb-3">{{ tutorial.title }}</h1>
                <p class="lead mb-3">{{ tutorial.description }}</p>
                
                <div class="d-flex flex-wrap gap-3 align-items-center">
                    <span class="badge bg-primary">
                        <i class="fas fa-layer-group me-1"></i>{{ tutorial.get_content_type_display }}
                    </span>
                    <span class="badge bg-secondary">
                        <i class="fas fa-clock me-1"></i>{{ tutorial.estimated_duration }} minutes
                    </span>
                    {% if tutorial.steps.count %}
                    <span class="badge bg-info">
                        <i class="fas fa-list-ol me-1"></i>{{ tutorial.steps.count }} steps
                    </span>
                    {% endif %}
                </div>
                
                <!-- Progress Bar -->
                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold">Progress</span>
                        <span class="fw-bold">{{ tutorial_progress.completion_percentage }}%</span>
                    </div>
                    <div class="progress-bar-custom">
                        <div class="progress-fill" style="width: {{ tutorial_progress.completion_percentage }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Achievement Badge -->
            {% if tutorial_progress.is_completed %}
            <div class="achievement-badge">
                <i class="fas fa-trophy me-2"></i>Tutorial Completed!
            </div>
            {% endif %}

            <!-- Tutorial Content -->
            <div class="tutorial-content">
                {% if tutorial.learning_objectives %}
                <div class="alert alert-info">
                    <h5><i class="fas fa-bullseye me-2"></i>Learning Objectives</h5>
                    <div>{{ tutorial.learning_objectives|linebreaks }}</div>
                </div>
                {% endif %}

                <div class="content-section">
                    {{ tutorial.content|linebreaks }}
                </div>

                {% if tutorial.summary %}
                <div class="alert alert-success mt-4">
                    <h5><i class="fas fa-check-circle me-2"></i>Summary</h5>
                    <div>{{ tutorial.summary|linebreaks }}</div>
                </div>
                {% endif %}
            </div>

            <!-- Quiz Section -->
            {% if quiz %}
            <div class="quiz-section">
                <h4><i class="fas fa-question-circle me-2"></i>Knowledge Check</h4>
                <p class="mb-3">Test your understanding with this quiz.</p>
                
                {% if quiz_attempts %}
                <div class="mb-3">
                    <h6>Your Previous Attempts:</h6>
                    {% for attempt in quiz_attempts|slice:":3" %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Attempt {{ attempt.attempt_number }}</span>
                        <span class="badge {% if attempt.is_passed %}bg-success{% else %}bg-warning{% endif %}">
                            {{ attempt.percentage }}%
                        </span>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                <a href="{% url 'education:quiz_detail' quiz.id %}" class="btn btn-primary">
                    <i class="fas fa-play me-2"></i>Take Quiz
                </a>
            </div>
            {% endif %}

            <!-- Navigation -->
            <div class="tutorial-navigation">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <!-- Previous Tutorial -->
                        {% comment %}
                        <!-- This would require additional logic to find previous/next tutorials -->
                        {% endcomment %}
                    </div>
                    
                    <div class="text-center">
                        {% if not tutorial_progress.is_completed %}
                        <button class="btn btn-success btn-lg" onclick="completeTutorial()">
                            <i class="fas fa-check me-2"></i>Mark as Complete
                        </button>
                        {% else %}
                        <span class="text-success fw-bold">
                            <i class="fas fa-check-circle me-2"></i>Completed
                        </span>
                        {% endif %}
                    </div>
                    
                    <div>
                        <a href="{% url 'education:learning_path_detail' tutorial.learning_path.id %}" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Path
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="tutorial-sidebar">
                <h5 class="mb-4">
                    <i class="fas fa-list me-2"></i>Tutorial Steps
                </h5>
                
                {% for step in steps %}
                <div class="step-item 
                    {% if step.id in completed_step_ids %}step-completed
                    {% elif step == current_step %}step-current
                    {% endif %}"
                    onclick="{% if step.id in completed_step_ids or step == current_step %}scrollToStep('step-{{ step.id }}'){% endif %}">
                    
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            {% if step.id in completed_step_ids %}
                            <i class="fas fa-check-circle text-success"></i>
                            {% elif step == current_step %}
                            <i class="fas fa-play-circle text-warning"></i>
                            {% else %}
                            <i class="fas fa-circle text-muted"></i>
                            {% endif %}
                        </div>
                        
                        <div class="flex-grow-1">
                            <div class="fw-bold">Step {{ step.order }}</div>
                            <div class="small text-muted">{{ step.title }}</div>
                            {% if step.step_type != 'content' %}
                            <span class="badge badge-sm bg-info">{{ step.get_step_type_display }}</span>
                            {% endif %}
                        </div>
                        
                        {% if not step.is_optional %}
                        <div class="ms-2">
                            <i class="fas fa-star text-warning" title="Required"></i>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <p class="text-muted">No steps defined for this tutorial.</p>
                {% endfor %}
                
                <!-- Tutorial Stats -->
                <div class="mt-4 pt-4 border-top">
                    <h6 class="mb-3">Tutorial Stats</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="fw-bold text-primary">{{ tutorial_progress.completion_percentage }}%</div>
                            <small class="text-muted">Complete</small>
                        </div>
                        <div class="col-6">
                            <div class="fw-bold text-info">{{ tutorial_progress.time_spent }}</div>
                            <small class="text-muted">Minutes</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function scrollToStep(stepId) {
    const element = document.getElementById(stepId);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    }
}

function completeTutorial() {
    if (confirm('Mark this tutorial as complete?')) {
        fetch('{% url "education:complete_tutorial_step" tutorial.id %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                'tutorial_id': '{{ tutorial.id }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while completing the tutorial.');
        });
    }
}

// Auto-save progress periodically
let progressTimer = setInterval(function() {
    // This could be used to track time spent and auto-save progress
    console.log('Progress auto-save (placeholder)');
}, 60000); // Every minute

// Cleanup timer when leaving page
window.addEventListener('beforeunload', function() {
    clearInterval(progressTimer);
});
</script>
{% endblock %}
