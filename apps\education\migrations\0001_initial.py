# Generated by Django 5.2.3 on 2025-07-04 10:45

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                (
                    "icon",
                    models.CharField(
                        blank=True, help_text="FontAwesome icon class", max_length=50
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#007bff", help_text="Hex color code", max_length=7
                    ),
                ),
                ("order", models.PositiveIntegerField(default=0)),
                ("is_active", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Categories",
                "ordering": ["order", "name"],
            },
        ),
        migrations.CreateModel(
            name="LearningPath",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "difficulty_level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                            ("expert", "Expert"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "estimated_duration",
                    models.PositiveIntegerField(
                        help_text="Estimated duration in minutes"
                    ),
                ),
                ("thumbnail", models.URLField(blank=True)),
                ("banner_image", models.URLField(blank=True)),
                (
                    "tags",
                    models.CharField(
                        blank=True, help_text="Comma-separated tags", max_length=500
                    ),
                ),
                ("is_published", models.BooleanField(default=False)),
                ("is_featured", models.BooleanField(default=False)),
                ("order", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="learning_paths",
                        to="education.category",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_paths",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "prerequisites",
                    models.ManyToManyField(
                        blank=True, related_name="unlocks", to="education.learningpath"
                    ),
                ),
            ],
            options={
                "ordering": ["order", "title"],
            },
        ),
        migrations.CreateModel(
            name="Quiz",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "time_limit",
                    models.PositiveIntegerField(
                        blank=True, help_text="Time limit in minutes", null=True
                    ),
                ),
                (
                    "passing_score",
                    models.PositiveIntegerField(
                        default=70,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ("max_attempts", models.PositiveIntegerField(default=3)),
                ("randomize_questions", models.BooleanField(default=True)),
                ("show_correct_answers", models.BooleanField(default=True)),
                ("is_published", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_quizzes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "learning_path",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quizzes",
                        to="education.learningpath",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Quizzes",
                "ordering": ["title"],
            },
        ),
        migrations.CreateModel(
            name="QuizAttempt",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("attempt_number", models.PositiveIntegerField(default=1)),
                ("score", models.FloatField(default=0.0)),
                ("max_score", models.FloatField(default=0.0)),
                (
                    "percentage",
                    models.PositiveIntegerField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ("is_completed", models.BooleanField(default=False)),
                ("is_passed", models.BooleanField(default=False)),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "time_taken",
                    models.PositiveIntegerField(
                        blank=True, help_text="Time taken in seconds", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "quiz",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attempts",
                        to="education.quiz",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quiz_attempts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "unique_together": {("user", "quiz", "attempt_number")},
            },
        ),
        migrations.CreateModel(
            name="QuizQuestion",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("question_text", models.TextField()),
                (
                    "question_type",
                    models.CharField(
                        choices=[
                            ("multiple_choice", "Multiple Choice"),
                            ("true_false", "True/False"),
                            ("short_answer", "Short Answer"),
                            ("numerical", "Numerical"),
                        ],
                        default="multiple_choice",
                        max_length=20,
                    ),
                ),
                ("points", models.PositiveIntegerField(default=1)),
                (
                    "explanation",
                    models.TextField(
                        blank=True, help_text="Explanation of the correct answer"
                    ),
                ),
                ("order", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "quiz",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="questions",
                        to="education.quiz",
                    ),
                ),
            ],
            options={
                "ordering": ["quiz", "order"],
                "unique_together": {("quiz", "order")},
            },
        ),
        migrations.CreateModel(
            name="QuizChoice",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("choice_text", models.TextField()),
                ("is_correct", models.BooleanField(default=False)),
                ("order", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="choices",
                        to="education.quizquestion",
                    ),
                ),
            ],
            options={
                "ordering": ["question", "order"],
            },
        ),
        migrations.CreateModel(
            name="Tutorial",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "content_type",
                    models.CharField(
                        choices=[
                            ("tutorial", "Tutorial"),
                            ("lesson", "Lesson"),
                            ("quiz", "Quiz"),
                            ("simulation", "Simulation"),
                            ("guide", "Guide"),
                            ("video", "Video"),
                        ],
                        default="tutorial",
                        max_length=20,
                    ),
                ),
                (
                    "difficulty_level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                            ("expert", "Expert"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "content",
                    models.TextField(help_text="Main tutorial content in Markdown"),
                ),
                (
                    "summary",
                    models.TextField(
                        blank=True, help_text="Brief summary of the tutorial"
                    ),
                ),
                (
                    "learning_objectives",
                    models.TextField(blank=True, help_text="What users will learn"),
                ),
                ("thumbnail", models.URLField(blank=True)),
                ("video_url", models.URLField(blank=True)),
                ("order", models.PositiveIntegerField(default=0)),
                (
                    "estimated_duration",
                    models.PositiveIntegerField(
                        help_text="Estimated duration in minutes"
                    ),
                ),
                (
                    "required_score",
                    models.PositiveIntegerField(
                        default=0, help_text="Minimum score to pass (for quizzes)"
                    ),
                ),
                ("is_published", models.BooleanField(default=False)),
                ("is_interactive", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_tutorials",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "learning_path",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tutorials",
                        to="education.learningpath",
                    ),
                ),
                (
                    "prerequisites",
                    models.ManyToManyField(
                        blank=True, related_name="unlocks", to="education.tutorial"
                    ),
                ),
            ],
            options={
                "ordering": ["learning_path", "order", "title"],
                "unique_together": {("learning_path", "order")},
            },
        ),
        migrations.AddField(
            model_name="quiz",
            name="tutorial",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="quizzes",
                to="education.tutorial",
            ),
        ),
        migrations.CreateModel(
            name="TutorialStep",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("content", models.TextField(help_text="Step content in Markdown")),
                (
                    "step_type",
                    models.CharField(
                        choices=[
                            ("content", "Content"),
                            ("action", "Action Required"),
                            ("quiz", "Quiz Question"),
                            ("simulation", "Trading Simulation"),
                            ("checkpoint", "Progress Checkpoint"),
                        ],
                        default="content",
                        max_length=20,
                    ),
                ),
                (
                    "action_required",
                    models.TextField(
                        blank=True, help_text="Description of required action"
                    ),
                ),
                (
                    "simulation_config",
                    models.JSONField(
                        blank=True,
                        help_text="Configuration for trading simulation",
                        null=True,
                    ),
                ),
                ("order", models.PositiveIntegerField(default=0)),
                ("is_optional", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "tutorial",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="steps",
                        to="education.tutorial",
                    ),
                ),
            ],
            options={
                "ordering": ["tutorial", "order"],
                "unique_together": {("tutorial", "order")},
            },
        ),
        migrations.CreateModel(
            name="QuizAnswer",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("text_answer", models.TextField(blank=True)),
                ("numerical_answer", models.FloatField(blank=True, null=True)),
                ("is_correct", models.BooleanField(default=False)),
                ("answered_at", models.DateTimeField(auto_now_add=True)),
                (
                    "attempt",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="answers",
                        to="education.quizattempt",
                    ),
                ),
                (
                    "selected_choice",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="education.quizchoice",
                    ),
                ),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="education.quizquestion",
                    ),
                ),
            ],
            options={
                "unique_together": {("attempt", "question")},
            },
        ),
        migrations.CreateModel(
            name="TutorialProgress",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "completion_percentage",
                    models.PositiveIntegerField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ("is_started", models.BooleanField(default=False)),
                ("is_completed", models.BooleanField(default=False)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
                (
                    "time_spent",
                    models.PositiveIntegerField(
                        default=0, help_text="Time spent in minutes"
                    ),
                ),
                ("quiz_score", models.FloatField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "tutorial",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_progress",
                        to="education.tutorial",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tutorial_progress",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "completed_steps",
                    models.ManyToManyField(
                        blank=True,
                        related_name="completed_by_users",
                        to="education.tutorialstep",
                    ),
                ),
                (
                    "current_step",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="education.tutorialstep",
                    ),
                ),
            ],
            options={
                "ordering": ["-last_accessed"],
                "unique_together": {("user", "tutorial")},
            },
        ),
        migrations.CreateModel(
            name="UserProgress",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "completion_percentage",
                    models.PositiveIntegerField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ("is_started", models.BooleanField(default=False)),
                ("is_completed", models.BooleanField(default=False)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
                (
                    "total_time_spent",
                    models.PositiveIntegerField(
                        default=0, help_text="Total time spent in minutes"
                    ),
                ),
                ("average_quiz_score", models.FloatField(default=0.0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "completed_tutorials",
                    models.ManyToManyField(
                        blank=True,
                        related_name="completed_by_users",
                        to="education.tutorial",
                    ),
                ),
                (
                    "current_tutorial",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="education.tutorial",
                    ),
                ),
                (
                    "learning_path",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_progress",
                        to="education.learningpath",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="learning_progress",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-last_accessed"],
                "unique_together": {("user", "learning_path")},
            },
        ),
    ]
