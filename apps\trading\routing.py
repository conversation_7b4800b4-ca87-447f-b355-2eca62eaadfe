"""
WebSocket URL routing for trading app.
"""

from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    re_path(r'ws/trading/portfolio/(?P<account_id>[0-9a-f-]+)/$', consumers.PortfolioConsumer.as_asgi()),
    re_path(r'ws/trading/orders/(?P<account_id>[0-9a-f-]+)/$', consumers.OrderConsumer.as_asgi()),
    re_path(r'ws/trading/risk/(?P<account_id>[0-9a-f-]+)/$', consumers.RiskConsumer.as_asgi()),
    re_path(r'ws/trading/prices/$', consumers.PriceConsumer.as_asgi()),
]
