from django.contrib import admin
from django.utils.html import format_html
from .models import Account, Transaction, Portfolio, Position, PortfolioHistory, Order, Trade, RiskManagement


@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    """
    Admin interface for Account model.
    """
    list_display = [
        'name', 'user', 'account_type', 'status',
        'current_balance_display', 'equity_display',
        'total_return_display', 'created_at'
    ]
    list_filter = ['account_type', 'status', 'created_at']
    search_fields = ['name', 'user__username', 'user__email']
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'last_activity',
        'total_deposits', 'total_withdrawals', 'realized_pnl'
    ]

    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'user', 'name', 'description', 'account_type', 'status')
        }),
        ('Balance Information', {
            'fields': (
                'initial_balance', 'current_balance', 'equity',
                'total_deposits', 'total_withdrawals',
                'realized_pnl', 'unrealized_pnl'
            )
        }),
        ('Account Limits', {
            'fields': ('max_balance', 'daily_deposit_limit')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'last_activity')
        }),
    )

    def current_balance_display(self, obj):
        """Display current balance with formatting."""
        return format_html(
            '<span style="color: {};">${:,.2f}</span>',
            'green' if obj.current_balance >= obj.initial_balance else 'red',
            obj.current_balance
        )
    current_balance_display.short_description = 'Current Balance'

    def equity_display(self, obj):
        """Display equity with formatting."""
        return format_html(
            '<span style="color: {};">${:,.2f}</span>',
            'green' if obj.equity >= obj.initial_balance else 'red',
            obj.equity
        )
    equity_display.short_description = 'Equity'

    def total_return_display(self, obj):
        """Display total return percentage with formatting."""
        return_pct = obj.total_return
        return format_html(
            '<span style="color: {};">{:+.2f}%</span>',
            'green' if return_pct >= 0 else 'red',
            return_pct
        )
    total_return_display.short_description = 'Total Return'


@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    """
    Admin interface for Transaction model.
    """
    list_display = [
        'id', 'account_name', 'transaction_type', 'amount_display',
        'balance_after', 'status', 'created_at'
    ]
    list_filter = ['transaction_type', 'status', 'created_at', 'account__account_type']
    search_fields = [
        'account__name', 'account__user__username',
        'description', 'reference_id', 'symbol'
    ]
    readonly_fields = [
        'id', 'created_at', 'processed_at', 'balance_before', 'balance_after'
    ]

    fieldsets = (
        ('Transaction Information', {
            'fields': ('id', 'account', 'transaction_type', 'status', 'amount')
        }),
        ('Balance Information', {
            'fields': ('balance_before', 'balance_after')
        }),
        ('Additional Details', {
            'fields': ('description', 'reference_id')
        }),
        ('Trading Information', {
            'fields': ('symbol', 'quantity', 'price_per_share'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'processed_at')
        }),
    )

    def account_name(self, obj):
        """Display account name with user."""
        return f"{obj.account.name} ({obj.account.user.username})"
    account_name.short_description = 'Account'

    def amount_display(self, obj):
        """Display amount with proper formatting and color."""
        color = 'green' if obj.is_credit else 'red'
        sign = '+' if obj.is_credit else '-'
        return format_html(
            '<span style="color: {};">{}{:,.2f}</span>',
            color, sign, abs(obj.amount)
        )
    amount_display.short_description = 'Amount'


@admin.register(Portfolio)
class PortfolioAdmin(admin.ModelAdmin):
    """
    Admin interface for Portfolio model.
    """
    list_display = [
        'account_name', 'total_value_display', 'total_cost_display',
        'unrealized_pnl_display', 'total_return_display', 'position_count', 'updated_at'
    ]
    list_filter = ['account__account_type', 'created_at']
    search_fields = ['account__name', 'account__user__username']
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'last_calculated',
        'total_value', 'total_cost', 'unrealized_pnl', 'realized_pnl'
    ]

    fieldsets = (
        ('Account Information', {
            'fields': ('id', 'account')
        }),
        ('Portfolio Metrics', {
            'fields': (
                'total_value', 'total_cost', 'unrealized_pnl', 'realized_pnl',
                'day_change', 'day_change_percent'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'last_calculated')
        }),
    )

    def account_name(self, obj):
        """Display account name with user."""
        return f"{obj.account.name} ({obj.account.user.username})"
    account_name.short_description = 'Account'

    def total_value_display(self, obj):
        """Display total value with formatting."""
        return format_html(
            '<span style="color: {};">${:,.2f}</span>',
            'green' if obj.total_value >= obj.total_cost else 'red',
            obj.total_value
        )
    total_value_display.short_description = 'Total Value'

    def total_cost_display(self, obj):
        """Display total cost with formatting."""
        return f"${obj.total_cost:,.2f}"
    total_cost_display.short_description = 'Total Cost'

    def unrealized_pnl_display(self, obj):
        """Display unrealized P&L with formatting."""
        return format_html(
            '<span style="color: {};">{:+,.2f}</span>',
            'green' if obj.unrealized_pnl >= 0 else 'red',
            obj.unrealized_pnl
        )
    unrealized_pnl_display.short_description = 'Unrealized P&L'

    def total_return_display(self, obj):
        """Display total return percentage with formatting."""
        return_pct = obj.total_return
        return format_html(
            '<span style="color: {};">{:+.2f}%</span>',
            'green' if return_pct >= 0 else 'red',
            return_pct
        )
    total_return_display.short_description = 'Total Return'


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    """
    Admin interface for Position model.
    """
    list_display = [
        'symbol', 'company_name', 'portfolio_account', 'quantity',
        'average_price', 'current_price', 'current_value_display',
        'unrealized_pnl_display', 'position_type'
    ]
    list_filter = ['position_type', 'portfolio__account__account_type', 'opened_at']
    search_fields = ['symbol', 'company_name', 'portfolio__account__name', 'portfolio__account__user__username']
    readonly_fields = [
        'id', 'opened_at', 'updated_at', 'last_price_update',
        'total_cost', 'unrealized_pnl'
    ]

    fieldsets = (
        ('Position Information', {
            'fields': ('id', 'portfolio', 'symbol', 'company_name', 'position_type')
        }),
        ('Quantity and Pricing', {
            'fields': ('quantity', 'average_price', 'current_price', 'total_cost')
        }),
        ('Performance', {
            'fields': ('unrealized_pnl', 'day_change', 'day_change_percent')
        }),
        ('Timestamps', {
            'fields': ('opened_at', 'updated_at', 'last_price_update')
        }),
    )

    def portfolio_account(self, obj):
        """Display portfolio account name."""
        return f"{obj.portfolio.account.name}"
    portfolio_account.short_description = 'Account'

    def current_value_display(self, obj):
        """Display current value with formatting."""
        return f"${obj.current_value:,.2f}"
    current_value_display.short_description = 'Current Value'

    def unrealized_pnl_display(self, obj):
        """Display unrealized P&L with formatting."""
        return format_html(
            '<span style="color: {};">{:+,.2f}</span>',
            'green' if obj.unrealized_pnl >= 0 else 'red',
            obj.unrealized_pnl
        )
    unrealized_pnl_display.short_description = 'Unrealized P&L'


@admin.register(PortfolioHistory)
class PortfolioHistoryAdmin(admin.ModelAdmin):
    """
    Admin interface for PortfolioHistory model.
    """
    list_display = [
        'portfolio_account', 'date', 'total_value_display',
        'day_change_display', 'total_return_display', 'position_count'
    ]
    list_filter = ['date', 'portfolio__account__account_type']
    search_fields = ['portfolio__account__name', 'portfolio__account__user__username']
    readonly_fields = ['id', 'created_at']
    date_hierarchy = 'date'

    fieldsets = (
        ('Snapshot Information', {
            'fields': ('id', 'portfolio', 'date')
        }),
        ('Portfolio Values', {
            'fields': ('total_value', 'cash_value', 'positions_value')
        }),
        ('Performance Metrics', {
            'fields': ('day_change', 'day_change_percent', 'total_return', 'position_count')
        }),
        ('Timestamps', {
            'fields': ('created_at',)
        }),
    )

    def portfolio_account(self, obj):
        """Display portfolio account name."""
        return f"{obj.portfolio.account.name}"
    portfolio_account.short_description = 'Account'

    def total_value_display(self, obj):
        """Display total value with formatting."""
        return f"${obj.total_value:,.2f}"
    total_value_display.short_description = 'Total Value'

    def day_change_display(self, obj):
        """Display day change with formatting."""
        return format_html(
            '<span style="color: {};">{:+,.2f} ({:+.2f}%)</span>',
            'green' if obj.day_change >= 0 else 'red',
            obj.day_change,
            obj.day_change_percent
        )
    day_change_display.short_description = 'Day Change'

    def total_return_display(self, obj):
        """Display total return with formatting."""
        return format_html(
            '<span style="color: {};">{:+.2f}%</span>',
            'green' if obj.total_return >= 0 else 'red',
            obj.total_return
        )
    total_return_display.short_description = 'Total Return'


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    """
    Admin interface for Order model.
    """
    list_display = [
        'order_display', 'account_name', 'symbol', 'order_type', 'side',
        'quantity', 'price_display', 'status_display', 'filled_progress',
        'created_at'
    ]
    list_filter = ['order_type', 'side', 'status', 'time_in_force', 'created_at']
    search_fields = ['symbol', 'account__name', 'account__user__username']
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'filled_at', 'filled_quantity',
        'average_fill_price', 'total_cost', 'estimated_cost'
    ]

    fieldsets = (
        ('Order Information', {
            'fields': ('id', 'account', 'symbol', 'order_type', 'side')
        }),
        ('Quantity and Pricing', {
            'fields': ('quantity', 'price', 'stop_price', 'time_in_force')
        }),
        ('Execution Details', {
            'fields': ('status', 'filled_quantity', 'average_fill_price', 'filled_at')
        }),
        ('Financial Details', {
            'fields': ('estimated_cost', 'commission', 'total_cost')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'expires_at')
        }),
        ('Additional Information', {
            'fields': ('notes',)
        }),
    )

    def order_display(self, obj):
        """Display order summary."""
        return f"{obj.get_side_display()} {obj.quantity} {obj.symbol}"
    order_display.short_description = 'Order'

    def account_name(self, obj):
        """Display account name with user."""
        return f"{obj.account.name} ({obj.account.user.username})"
    account_name.short_description = 'Account'

    def price_display(self, obj):
        """Display price information."""
        if obj.order_type == 'market':
            return 'Market'
        elif obj.price:
            return f"${obj.price:.2f}"
        return "—"
    price_display.short_description = 'Price'

    def status_display(self, obj):
        """Display status with color formatting."""
        status_colors = {
            'pending': 'warning',
            'partially_filled': 'info',
            'filled': 'success',
            'cancelled': 'secondary',
            'rejected': 'danger',
            'expired': 'dark'
        }
        color = status_colors.get(obj.status, 'secondary')
        return format_html(
            '<span class="badge bg-{}">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = 'Status'

    def filled_progress(self, obj):
        """Display fill progress."""
        if obj.quantity > 0:
            percentage = (obj.filled_quantity / obj.quantity) * 100
            return format_html(
                '{}/{} ({:.1f}%)',
                obj.filled_quantity,
                obj.quantity,
                percentage
            )
        return "0/0"
    filled_progress.short_description = 'Filled'

    actions = ['cancel_selected_orders']

    def cancel_selected_orders(self, request, queryset):
        """Cancel selected orders."""
        cancelled_count = 0
        for order in queryset:
            if order.can_cancel():
                order.cancel_order("Admin cancelled")
                cancelled_count += 1

        self.message_user(
            request,
            f"Successfully cancelled {cancelled_count} orders."
        )
    cancel_selected_orders.short_description = "Cancel selected orders"


@admin.register(Trade)
class TradeAdmin(admin.ModelAdmin):
    """
    Admin interface for Trade model.
    """
    list_display = [
        'trade_display', 'order_summary', 'symbol', 'side',
        'quantity', 'price_display', 'gross_amount_display',
        'commission_display', 'net_amount_display', 'executed_at'
    ]
    list_filter = ['side', 'executed_at', 'execution_venue']
    search_fields = ['symbol', 'order__account__name', 'order__account__user__username']
    readonly_fields = [
        'id', 'executed_at', 'gross_amount', 'net_amount'
    ]

    fieldsets = (
        ('Trade Information', {
            'fields': ('id', 'order', 'symbol', 'side')
        }),
        ('Execution Details', {
            'fields': ('quantity', 'price', 'executed_at', 'execution_venue')
        }),
        ('Financial Details', {
            'fields': ('gross_amount', 'commission', 'net_amount')
        }),
        ('Additional Information', {
            'fields': ('notes',)
        }),
    )

    def trade_display(self, obj):
        """Display trade summary."""
        return f"{obj.get_side_display()} {obj.quantity} {obj.symbol}"
    trade_display.short_description = 'Trade'

    def order_summary(self, obj):
        """Display related order information."""
        return f"Order #{str(obj.order.id)[:8]}..."
    order_summary.short_description = 'Order'

    def price_display(self, obj):
        """Display execution price."""
        return f"${obj.price:.4f}"
    price_display.short_description = 'Price'

    def gross_amount_display(self, obj):
        """Display gross amount."""
        return f"${obj.gross_amount:,.2f}"
    gross_amount_display.short_description = 'Gross Amount'

    def commission_display(self, obj):
        """Display commission."""
        return f"${obj.commission:.2f}"
    commission_display.short_description = 'Commission'

    def net_amount_display(self, obj):
        """Display net amount with color."""
        color = 'green' if obj.side == 'sell' else 'red'
        return format_html(
            '<span style="color: {};">${:,.2f}</span>',
            color,
            obj.net_amount
        )
    net_amount_display.short_description = 'Net Amount'


@admin.register(RiskManagement)
class RiskManagementAdmin(admin.ModelAdmin):
    """
    Admin interface for Risk Management model.
    """
    list_display = [
        'account_name', 'max_position_size_percent', 'max_daily_loss_percent',
        'max_drawdown_percent', 'max_leverage', 'enable_risk_alerts',
        'updated_at'
    ]
    list_filter = ['enable_risk_alerts', 'enable_drawdown_alerts', 'enable_position_size_alerts']
    search_fields = ['account__name', 'account__user__username']
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        ('Account', {
            'fields': ('id', 'account')
        }),
        ('Position Sizing', {
            'fields': ('max_position_size_percent', 'max_position_size_amount')
        }),
        ('Risk Limits', {
            'fields': ('max_daily_loss_percent', 'max_daily_loss_amount', 'max_drawdown_percent')
        }),
        ('Risk/Reward Settings', {
            'fields': ('min_risk_reward_ratio',)
        }),
        ('Leverage Settings', {
            'fields': ('max_leverage', 'margin_requirement_percent')
        }),
        ('Alert Settings', {
            'fields': ('enable_risk_alerts', 'enable_drawdown_alerts', 'enable_position_size_alerts')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def account_name(self, obj):
        """Display account name with user."""
        return f"{obj.account.name} ({obj.account.user.username})"
    account_name.short_description = 'Account'
