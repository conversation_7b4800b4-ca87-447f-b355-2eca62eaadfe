# Generated migration for market data performance optimization indexes

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('market_data', '0001_initial'),
    ]

    operations = [
        # Symbol indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_symbols_symbol_active ON market_symbols(symbol, is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_symbols_symbol_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_symbols_active_tradeable ON market_symbols(is_active, is_tradeable);",
            reverse_sql="DROP INDEX IF EXISTS idx_symbols_active_tradeable;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_symbols_exchange_sector ON market_symbols(exchange_id, sector_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_symbols_exchange_sector;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_symbols_type_active ON market_symbols(symbol_type, is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_symbols_type_active;"
        ),
        
        # Market Data indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_market_data_symbol_latest ON market_data(symbol_id, is_latest);",
            reverse_sql="DROP INDEX IF EXISTS idx_market_data_symbol_latest;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp ON market_data(symbol_id, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_market_data_symbol_timestamp;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_market_data_latest_timestamp ON market_data(is_latest, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_market_data_latest_timestamp;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_market_data_timestamp;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_market_data_source_timestamp ON market_data(data_source, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_market_data_source_timestamp;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_market_data_market_hours ON market_data(is_market_hours, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_market_data_market_hours;"
        ),
        
        # Exchange indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_exchanges_code_active ON market_exchanges(code, is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_exchanges_code_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_exchanges_timezone ON market_exchanges(timezone);",
            reverse_sql="DROP INDEX IF EXISTS idx_exchanges_timezone;"
        ),
        
        # Sector indexes
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_sectors_name ON market_sectors(name);",
            reverse_sql="DROP INDEX IF EXISTS idx_sectors_name;"
        ),
        

    ]
