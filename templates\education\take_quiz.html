{% extends 'base.html' %}
{% load static %}

{% block title %}Take Quiz: {{ quiz.title }} - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
.quiz-container {
    max-width: 800px;
    margin: 0 auto;
}

.quiz-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
}

.question-card {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.question-number {
    background: #007bff;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 20px;
}

.choice-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.choice-option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.choice-option.selected {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.choice-option input[type="radio"] {
    margin-right: 10px;
}

.quiz-progress {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    position: sticky;
    top: 20px;
}

.progress-bar-custom {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    transition: width 0.3s ease;
}

.timer-display {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    margin-bottom: 20px;
}

.timer-warning {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.navigation-buttons {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    bottom: 20px;
    z-index: 100;
}

.text-answer {
    width: 100%;
    min-height: 100px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-family: inherit;
    resize: vertical;
}

.text-answer:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.numerical-answer {
    width: 200px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-size: 1.1rem;
    text-align: center;
}

.numerical-answer:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.question-navigation {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.question-nav-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.question-nav-btn:hover {
    border-color: #007bff;
}

.question-nav-btn.answered {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.question-nav-btn.current {
    background: #007bff;
    color: white;
    border-color: #007bff;
}
</style>
{% endblock %}

{% block content %}
<div class="quiz-container">
    <!-- Quiz Header -->
    <div class="quiz-header">
        <h1 class="display-6 fw-bold mb-3">{{ quiz.title }}</h1>
        <p class="lead mb-0">Attempt {{ attempt.attempt_number }} of {{ quiz.max_attempts }}</p>
    </div>

    <!-- Timer (if applicable) -->
    {% if quiz.time_limit %}
    <div class="timer-display" id="timer-display">
        <h5 class="mb-2">
            <i class="fas fa-clock me-2"></i>Time Remaining
        </h5>
        <div class="h4 mb-0" id="timer-countdown">{{ quiz.time_limit }}:00</div>
    </div>
    {% endif %}

    <!-- Quiz Form -->
    <form method="post" id="quiz-form">
        {% csrf_token %}
        <input type="hidden" name="attempt_id" value="{{ attempt.id }}">

        <!-- Questions -->
        {% for question in questions %}
        <div class="question-card" id="question-{{ forloop.counter }}">
            <div class="question-number">{{ forloop.counter }}</div>
            
            <h4 class="mb-4">{{ question.question_text }}</h4>
            
            {% if question.question_type == 'multiple_choice' or question.question_type == 'true_false' %}
                {% for choice in question.choices.all %}
                <div class="choice-option" onclick="selectChoice(this)">
                    <input type="radio" name="question_{{ question.id }}" value="{{ choice.id }}" id="choice_{{ choice.id }}">
                    <label for="choice_{{ choice.id }}" class="mb-0 cursor-pointer">{{ choice.choice_text }}</label>
                </div>
                {% endfor %}
            
            {% elif question.question_type == 'short_answer' %}
                <textarea name="question_{{ question.id }}" class="text-answer" 
                         placeholder="Enter your answer here..." 
                         onchange="markAnswered({{ forloop.counter }})"></textarea>
            
            {% elif question.question_type == 'numerical' %}
                <div class="text-center">
                    <input type="number" name="question_{{ question.id }}" class="numerical-answer" 
                           placeholder="Enter number" step="any"
                           onchange="markAnswered({{ forloop.counter }})">
                </div>
            {% endif %}
            
            {% if question.points > 1 %}
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-star me-1"></i>Worth {{ question.points }} points
                </small>
            </div>
            {% endif %}
        </div>
        {% endfor %}

        <!-- Navigation -->
        <div class="navigation-buttons">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-secondary" onclick="previousQuestion()" id="prev-btn" disabled>
                            <i class="fas fa-chevron-left me-2"></i>Previous
                        </button>
                        <button type="button" class="btn btn-primary" onclick="nextQuestion()" id="next-btn">
                            Next<i class="fas fa-chevron-right ms-2"></i>
                        </button>
                    </div>
                </div>
                
                <div class="col-md-6 text-end">
                    <button type="submit" class="btn btn-success btn-lg" onclick="return confirmSubmit()">
                        <i class="fas fa-check me-2"></i>Submit Quiz
                    </button>
                </div>
            </div>
            
            <!-- Question Navigation -->
            <div class="question-navigation">
                <small class="text-muted me-3">Jump to question:</small>
                {% for question in questions %}
                <div class="question-nav-btn" onclick="goToQuestion({{ forloop.counter }})" 
                     id="nav-btn-{{ forloop.counter }}">
                    {{ forloop.counter }}
                </div>
                {% endfor %}
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentQuestion = 1;
const totalQuestions = {{ questions.count }};
const timeLimit = {% if quiz.time_limit %}{{ quiz.time_limit }} * 60{% else %}null{% endif %}; // in seconds
let timeRemaining = timeLimit;
let timerInterval;

document.addEventListener('DOMContentLoaded', function() {
    showQuestion(1);
    
    {% if quiz.time_limit %}
    startTimer();
    {% endif %}
    
    // Auto-save answers periodically
    setInterval(saveProgress, 30000); // Every 30 seconds
});

function showQuestion(questionNum) {
    // Hide all questions
    for (let i = 1; i <= totalQuestions; i++) {
        const question = document.getElementById(`question-${i}`);
        if (question) {
            question.style.display = i === questionNum ? 'block' : 'none';
        }
        
        // Update navigation buttons
        const navBtn = document.getElementById(`nav-btn-${i}`);
        if (navBtn) {
            navBtn.classList.toggle('current', i === questionNum);
        }
    }
    
    currentQuestion = questionNum;
    
    // Update navigation buttons
    document.getElementById('prev-btn').disabled = questionNum === 1;
    document.getElementById('next-btn').style.display = questionNum === totalQuestions ? 'none' : 'inline-block';
    
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function nextQuestion() {
    if (currentQuestion < totalQuestions) {
        showQuestion(currentQuestion + 1);
    }
}

function previousQuestion() {
    if (currentQuestion > 1) {
        showQuestion(currentQuestion - 1);
    }
}

function goToQuestion(questionNum) {
    showQuestion(questionNum);
}

function selectChoice(choiceElement) {
    // Remove selected class from siblings
    const siblings = choiceElement.parentNode.querySelectorAll('.choice-option');
    siblings.forEach(sibling => sibling.classList.remove('selected'));
    
    // Add selected class to clicked element
    choiceElement.classList.add('selected');
    
    // Check the radio button
    const radio = choiceElement.querySelector('input[type="radio"]');
    if (radio) {
        radio.checked = true;
        markAnswered(currentQuestion);
    }
}

function markAnswered(questionNum) {
    const navBtn = document.getElementById(`nav-btn-${questionNum}`);
    if (navBtn) {
        navBtn.classList.add('answered');
    }
}

function confirmSubmit() {
    const answeredQuestions = document.querySelectorAll('.question-nav-btn.answered').length;
    const unansweredCount = totalQuestions - answeredQuestions;
    
    if (unansweredCount > 0) {
        return confirm(`You have ${unansweredCount} unanswered question(s). Are you sure you want to submit?`);
    }
    
    return confirm('Are you sure you want to submit your quiz? You cannot change your answers after submission.');
}

{% if quiz.time_limit %}
function startTimer() {
    timerInterval = setInterval(function() {
        timeRemaining--;
        updateTimerDisplay();
        
        if (timeRemaining <= 0) {
            clearInterval(timerInterval);
            alert('Time is up! Your quiz will be submitted automatically.');
            document.getElementById('quiz-form').submit();
        }
    }, 1000);
}

function updateTimerDisplay() {
    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    document.getElementById('timer-countdown').textContent = display;
    
    // Change color when time is running low
    const timerDisplay = document.getElementById('timer-display');
    if (timeRemaining <= 300) { // 5 minutes
        timerDisplay.classList.add('timer-warning');
    }
}
{% endif %}

function saveProgress() {
    // This could be implemented to auto-save answers
    console.log('Auto-saving progress...');
}

// Prevent accidental page refresh
window.addEventListener('beforeunload', function(e) {
    e.preventDefault();
    e.returnValue = '';
});

// Handle form submission to remove beforeunload listener
document.getElementById('quiz-form').addEventListener('submit', function() {
    window.removeEventListener('beforeunload', function() {});
    {% if quiz.time_limit %}
    clearInterval(timerInterval);
    {% endif %}
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft' && currentQuestion > 1) {
        previousQuestion();
    } else if (e.key === 'ArrowRight' && currentQuestion < totalQuestions) {
        nextQuestion();
    }
});
</script>
{% endblock %}
