{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Trading Simulator{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        body {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        
        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .alert {
            border-radius: 0.5rem;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            margin-top: auto;
        }
        
        .main-content {
            min-height: calc(100vh - 200px);
        }
        
        .trading-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .balance-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        /* Notification Dropdown Styles */
        .notification-dropdown {
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-dropdown .dropdown-item {
            white-space: normal;
            border-bottom: 1px solid #f8f9fa;
        }

        .notification-dropdown .dropdown-item:last-child {
            border-bottom: none;
        }

        .notification-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="{% url 'accounts:dashboard' %}">
                <i class="fas fa-chart-line me-2"></i>Trading Simulator
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-chart-bar me-1"></i>Portfolio
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-exchange-alt me-1"></i>Trade
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'education:home' %}">
                                <i class="fas fa-graduation-cap me-1"></i>Learn
                            </a>
                        </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <!-- Notifications Dropdown -->
                        <li class="nav-item dropdown me-3">
                            <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                {% if unread_notification_count > 0 %}
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    {{ unread_notification_count }}
                                    <span class="visually-hidden">unread notifications</span>
                                </span>
                                {% endif %}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end notification-dropdown" style="width: 300px;">
                                <li class="dropdown-header d-flex justify-content-between align-items-center">
                                    <span>Notifications</span>
                                    <small class="text-muted">{{ unread_notification_count }} unread</small>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                {% for notification in recent_notifications %}
                                <li>
                                    <a class="dropdown-item py-2 {% if not notification.is_read %}bg-light{% endif %}" href="#">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0 me-2">
                                                {% if notification.notification_type == 'success' %}
                                                    <i class="fas fa-check-circle text-success"></i>
                                                {% elif notification.notification_type == 'warning' %}
                                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                                {% elif notification.notification_type == 'error' %}
                                                    <i class="fas fa-times-circle text-danger"></i>
                                                {% elif notification.notification_type == 'trade' %}
                                                    <i class="fas fa-chart-line text-primary"></i>
                                                {% else %}
                                                    <i class="fas fa-info-circle text-info"></i>
                                                {% endif %}
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="fw-bold small">{{ notification.title }}</div>
                                                <div class="text-muted small">{{ notification.message|truncatechars:50 }}</div>
                                                <div class="text-muted small">{{ notification.created_at|timesince }} ago</div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                {% empty %}
                                <li>
                                    <div class="dropdown-item text-center text-muted py-3">
                                        <i class="fas fa-bell-slash mb-2"></i><br>
                                        No notifications
                                    </div>
                                </li>
                                {% endfor %}
                                {% if recent_notifications|length >= 5 %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-center small" href="#">
                                        View all notifications
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </li>

                        <!-- User Profile Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i>{{ user.first_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}"><i class="fas fa-user me-2"></i>Profile</a></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:profile_edit' %}"><i class="fas fa-edit me-2"></i>Edit Profile</a></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:trading_preferences' %}"><i class="fas fa-cog me-2"></i>Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:register' %}">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb Navigation -->
    {% block breadcrumb %}
    {% if user.is_authenticated %}
    <nav aria-label="breadcrumb" class="bg-light border-bottom">
        <div class="container">
            <ol class="breadcrumb mb-0 py-2">
                <li class="breadcrumb-item">
                    <a href="{% url 'accounts:dashboard' %}" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>Dashboard
                    </a>
                </li>
                {% block breadcrumb_items %}{% endblock %}
            </ol>
        </div>
    </nav>
    {% endif %}
    {% endblock %}

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {% if message.tags == 'error' %}
                        <i class="fas fa-exclamation-triangle me-2"></i>
                    {% elif message.tags == 'success' %}
                        <i class="fas fa-check-circle me-2"></i>
                    {% elif message.tags == 'warning' %}
                        <i class="fas fa-exclamation-circle me-2"></i>
                    {% elif message.tags == 'info' %}
                        <i class="fas fa-info-circle me-2"></i>
                    {% endif %}
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="main-content flex-grow-1">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Trading Simulator</h5>
                    <p class="mb-0">Learn trading without the risk. Practice with virtual money and real market data.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 Trading Simulator. All rights reserved.</p>
                    <small class="text-muted">
                        <a href="#" class="text-light me-3">Privacy Policy</a>
                        <a href="#" class="text-light me-3">Terms of Service</a>
                        <a href="#" class="text-light">Contact</a>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
