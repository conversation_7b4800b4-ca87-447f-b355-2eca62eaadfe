"""
Management command for database optimization operations.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.trading.optimization import DatabaseOptimizer, PerformanceMonitor


class Command(BaseCommand):
    help = 'Perform database optimization operations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--analyze',
            action='store_true',
            help='Analyze database performance and suggest optimizations',
        )
        parser.add_argument(
            '--optimize',
            action='store_true',
            help='Perform database optimization operations',
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up old data to improve performance',
        )
        parser.add_argument(
            '--metrics',
            action='store_true',
            help='Display database performance metrics',
        )
        parser.add_argument(
            '--days-to-keep',
            type=int,
            default=90,
            help='Number of days of data to keep during cleanup (default: 90)',
        )

    def handle(self, *args, **options):
        """Handle database optimization operations."""
        self.stdout.write(f'Starting database optimization at {timezone.now()}')
        
        if options['analyze']:
            self.analyze_performance()
        
        if options['metrics']:
            self.show_metrics()
        
        if options['optimize']:
            self.optimize_database()
        
        if options['cleanup']:
            self.cleanup_data(options['days_to_keep'])
        
        if not any([options['analyze'], options['metrics'], options['optimize'], options['cleanup']]):
            self.stdout.write(
                self.style.WARNING('No operation specified. Use --help to see available options.')
            )
    
    def analyze_performance(self):
        """Analyze database performance."""
        self.stdout.write('Analyzing database performance...')
        
        try:
            analysis = DatabaseOptimizer.analyze_query_performance()
            
            self.stdout.write(
                self.style.SUCCESS('Database Analysis Results:')
            )
            
            self.stdout.write(f'Cache Size: {analysis["cache_size"]} pages')
            self.stdout.write(f'Page Size: {analysis["page_size"]} bytes')
            
            if analysis['recommendations']:
                self.stdout.write('\nRecommendations:')
                for rec in analysis['recommendations']:
                    self.stdout.write(f'  - {rec["type"]}: {rec["message"]}')
                    if 'action' in rec:
                        self.stdout.write(f'    Action: {rec["action"]}')
            else:
                self.stdout.write(
                    self.style.SUCCESS('No optimization recommendations at this time.')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error analyzing database: {str(e)}')
            )
    
    def show_metrics(self):
        """Show database performance metrics."""
        self.stdout.write('Gathering database metrics...')
        
        try:
            metrics = PerformanceMonitor.get_database_metrics()
            
            self.stdout.write(
                self.style.SUCCESS('Database Metrics:')
            )
            
            self.stdout.write(f'Database Size: {metrics["database_size_mb"]} MB')
            self.stdout.write(f'Total Records: {metrics["total_records"]:,}')
            
            self.stdout.write('\nTable Statistics:')
            for table, count in metrics['table_statistics'].items():
                self.stdout.write(f'  {table}: {count:,} records')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error gathering metrics: {str(e)}')
            )
    
    def optimize_database(self):
        """Perform database optimization."""
        self.stdout.write('Optimizing database...')
        
        try:
            success = DatabaseOptimizer.optimize_database()
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS('Database optimization completed successfully.')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('Database optimization completed with warnings.')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error optimizing database: {str(e)}')
            )
    
    def cleanup_data(self, days_to_keep):
        """Clean up old data."""
        self.stdout.write(f'Cleaning up data older than {days_to_keep} days...')
        
        try:
            cleanup_stats = PerformanceMonitor.cleanup_old_data(days_to_keep)
            
            self.stdout.write(
                self.style.SUCCESS('Data cleanup completed:')
            )
            
            for data_type, count in cleanup_stats.items():
                if count > 0:
                    self.stdout.write(f'  {data_type}: {count:,} records cleaned')
                else:
                    self.stdout.write(f'  {data_type}: No records to clean')
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error cleaning up data: {str(e)}')
            )
