{% extends 'base.html' %}
{% load static %}

{% block title %}{{ path.title }} - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
.learning-path-header {
    background: linear-gradient(135deg, {{ path.category.color }}22 0%, {{ path.category.color }}44 100%);
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 30px;
}

.difficulty-badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.difficulty-beginner { background-color: #28a745; color: white; }
.difficulty-intermediate { background-color: #ffc107; color: #212529; }
.difficulty-advanced { background-color: #fd7e14; color: white; }
.difficulty-expert { background-color: #dc3545; color: white; }

.tutorial-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.tutorial-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tutorial-completed {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

.tutorial-in-progress {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.tutorial-locked {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
    opacity: 0.7;
}

.progress-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
}

.progress-completed { background-color: #28a745; }
.progress-in-progress { background-color: #ffc107; }
.progress-not-started { background-color: #6c757d; }

.prerequisite-card {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: {{ path.category.color }};
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Learning Path Header -->
    <div class="learning-path-header">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="d-flex align-items-center mb-3">
                    <span class="badge difficulty-{{ path.difficulty_level }} difficulty-badge me-3">
                        {{ path.get_difficulty_level_display }}
                    </span>
                    <span class="badge bg-secondary">
                        <i class="fas fa-tag me-1"></i>{{ path.category.name }}
                    </span>
                </div>
                
                <h1 class="display-5 fw-bold mb-3">{{ path.title }}</h1>
                <p class="lead mb-4">{{ path.description }}</p>
                
                <div class="d-flex flex-wrap gap-3">
                    <span class="text-muted">
                        <i class="fas fa-clock me-2"></i>{{ path.estimated_duration }} minutes
                    </span>
                    <span class="text-muted">
                        <i class="fas fa-list me-2"></i>{{ tutorials.count }} tutorials
                    </span>
                    {% if path.tags %}
                    <span class="text-muted">
                        <i class="fas fa-tags me-2"></i>{{ path.tags }}
                    </span>
                    {% endif %}
                </div>
            </div>
            
            <div class="col-lg-4 text-center">
                <div class="progress-circle progress-{% if user_progress.is_completed %}completed{% elif user_progress.is_started %}in-progress{% else %}not-started{% endif %}">
                    {{ user_progress.completion_percentage }}%
                </div>
                <p class="mt-2 mb-0">
                    {% if user_progress.is_completed %}
                        Completed!
                    {% elif user_progress.is_started %}
                        In Progress
                    {% else %}
                        Not Started
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <!-- Prerequisites Check -->
    {% if not can_start and missing_prerequisites %}
    <div class="prerequisite-card">
        <h5 class="text-danger mb-3">
            <i class="fas fa-lock me-2"></i>Prerequisites Required
        </h5>
        <p class="mb-3">You need to complete the following learning paths before starting this one:</p>
        <ul class="list-unstyled">
            {% for prereq in missing_prerequisites %}
            <li class="mb-2">
                <i class="fas fa-arrow-right me-2"></i>
                <a href="{% url 'education:learning_path_detail' prereq.id %}">{{ prereq.title }}</a>
            </li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}

    <!-- Learning Path Stats -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ tutorials.count }}</div>
            <div class="stat-label">Total Tutorials</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ user_progress.completed_tutorials.count }}</div>
            <div class="stat-label">Completed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ path.estimated_duration }}</div>
            <div class="stat-label">Minutes</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ user_progress.total_time_spent }}</div>
            <div class="stat-label">Time Spent</div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            {% if can_start %}
                {% if not user_progress.is_started %}
                <form method="post" action="{% url 'education:start_learning_path' path.id %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-play me-2"></i>Start Learning Path
                    </button>
                </form>
                {% elif user_progress.current_tutorial %}
                <a href="{% url 'education:tutorial_detail' user_progress.current_tutorial.id %}" 
                   class="btn btn-success btn-lg me-3">
                    <i class="fas fa-arrow-right me-2"></i>Continue Learning
                </a>
                {% endif %}
            {% endif %}
            
            <a href="{% url 'education:learning_paths' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Learning Paths
            </a>
        </div>
    </div>

    <!-- Tutorials List -->
    <div class="row">
        <div class="col-12">
            <h3 class="mb-4">
                <i class="fas fa-list me-2"></i>Tutorials
            </h3>
            
            {% for tutorial in tutorials %}
            <div class="tutorial-card 
                {% if tutorial in user_progress.completed_tutorials.all %}tutorial-completed
                {% elif tutorial.id in tutorial_progress %}tutorial-in-progress
                {% elif not can_start %}tutorial-locked
                {% endif %}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-primary me-2">{{ tutorial.order }}</span>
                                <h5 class="card-title mb-0">{{ tutorial.title }}</h5>
                                
                                {% if tutorial in user_progress.completed_tutorials.all %}
                                <i class="fas fa-check-circle text-success ms-2"></i>
                                {% elif tutorial.id in tutorial_progress %}
                                <i class="fas fa-play-circle text-warning ms-2"></i>
                                {% endif %}
                            </div>
                            
                            <p class="card-text text-muted mb-2">{{ tutorial.description }}</p>
                            
                            <div class="d-flex flex-wrap gap-3">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>{{ tutorial.estimated_duration }} min
                                </small>
                                <small class="text-muted">
                                    <i class="fas fa-layer-group me-1"></i>{{ tutorial.get_content_type_display }}
                                </small>
                                {% if tutorial.steps.count %}
                                <small class="text-muted">
                                    <i class="fas fa-list-ol me-1"></i>{{ tutorial.steps.count }} steps
                                </small>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4 text-end">
                            {% if tutorial.id in tutorial_progress %}
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar"
                                         style="width: {{ tutorial_progress.tutorial.id.completion_percentage|default:0 }}%"
                                         aria-valuenow="{{ tutorial_progress.tutorial.id.completion_percentage|default:0 }}"
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ tutorial_progress.tutorial.id.completion_percentage|default:0 }}%
                                    </div>
                                </div>
                            {% endif %}
                            
                            {% if can_start and user_progress.is_started %}
                            <a href="{% url 'education:tutorial_detail' tutorial.id %}"
                               class="btn btn-primary">
                                {% if tutorial in user_progress.completed_tutorials.all %}
                                    <i class="fas fa-eye me-2"></i>Review
                                {% else %}
                                    <i class="fas fa-play me-2"></i>Start
                                {% endif %}
                            </a>
                            {% else %}
                            <button class="btn btn-secondary" disabled>
                                <i class="fas fa-lock me-2"></i>Locked
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                No tutorials available for this learning path yet.
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.transition = 'width 1s ease-in-out';
            bar.style.width = width;
        }, 100);
    });
});
</script>
{% endblock %}
