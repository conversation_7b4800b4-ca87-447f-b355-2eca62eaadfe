{% extends 'base.html' %}
{% load static %}

{% block title %}Bracket Order - {{ account.name }} - Trading Simulator{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item">
    <a href="{% url 'trading:account_overview' %}" class="text-decoration-none">
        <i class="fas fa-wallet me-1"></i>Trading
    </a>
</li>
<li class="breadcrumb-item">
    <a href="{% url 'trading:account_detail' account.id %}" class="text-decoration-none">
        {{ account.name }}
    </a>
</li>
<li class="breadcrumb-item active" aria-current="page">
    Bracket Order
</li>
{% endblock %}

{% block extra_css %}
<style>
    .bracket-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    
    .bracket-form-card {
        border-left: 4px solid #667eea;
    }
    
    .bracket-preview-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 15px;
    }
    
    .price-level {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 4px solid #28a745;
    }
    
    .price-level.stop-loss {
        border-left-color: #dc3545;
    }
    
    .price-level.take-profit {
        border-left-color: #28a745;
    }
    
    .price-level.entry {
        border-left-color: #007bff;
    }
    
    .risk-reward-display {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
    }
    
    .field-group {
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 col-lg-2 dashboard-sidebar p-0">
            <div class="p-3">
                <div class="sidebar-section-title">Trading</div>
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="{% url 'trading:account_overview' %}" class="nav-link">
                            <i class="fas fa-wallet"></i>
                            Accounts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:order_entry' %}" class="nav-link">
                            <i class="fas fa-plus-circle"></i>
                            Place Order
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:bracket_order_entry' %}" class="nav-link active">
                            <i class="fas fa-layer-group"></i>
                            Bracket Order
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:order_list' %}" class="nav-link">
                            <i class="fas fa-list-alt"></i>
                            Orders
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:risk_management' %}" class="nav-link">
                            <i class="fas fa-shield-alt"></i>
                            Risk Management
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 p-4">
            <!-- Bracket Header -->
            <div class="bracket-header p-4 mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1><i class="fas fa-layer-group me-3"></i>Bracket Order</h1>
                        <p class="mb-0">Create entry, stop loss, and take profit orders in one transaction</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="{% url 'trading:order_entry' %}" class="btn btn-light">
                            <i class="fas fa-plus me-2"></i>Regular Order
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Bracket Form -->
                <div class="col-lg-8">
                    <div class="card bracket-form-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Bracket Order Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" id="bracketForm">
                                {% csrf_token %}
                                
                                <!-- Symbol -->
                                <div class="field-group">
                                    <label for="{{ form.symbol.id_for_label }}" class="form-label">
                                        Stock Symbol <span class="text-danger">*</span>
                                    </label>
                                    {{ form.symbol }}
                                    {% if form.symbol.errors %}
                                    <div class="text-danger">{{ form.symbol.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <!-- Side and Quantity -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="field-group">
                                            <label for="{{ form.side.id_for_label }}" class="form-label">
                                                Order Side <span class="text-danger">*</span>
                                            </label>
                                            {{ form.side }}
                                            {% if form.side.errors %}
                                            <div class="text-danger">{{ form.side.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="field-group">
                                            <label for="{{ form.quantity.id_for_label }}" class="form-label">
                                                Quantity (Shares) <span class="text-danger">*</span>
                                            </label>
                                            {{ form.quantity }}
                                            {% if form.quantity.errors %}
                                            <div class="text-danger">{{ form.quantity.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Price Levels -->
                                <div class="field-group">
                                    <label for="{{ form.entry_price.id_for_label }}" class="form-label">
                                        Entry Price ($) <span class="text-danger">*</span>
                                    </label>
                                    {{ form.entry_price }}
                                    <div class="form-text">Price at which you want to enter the position</div>
                                    {% if form.entry_price.errors %}
                                    <div class="text-danger">{{ form.entry_price.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="field-group">
                                            <label for="{{ form.stop_loss_price.id_for_label }}" class="form-label">
                                                Stop Loss Price ($) <span class="text-danger">*</span>
                                            </label>
                                            {{ form.stop_loss_price }}
                                            <div class="form-text">Price to limit losses</div>
                                            {% if form.stop_loss_price.errors %}
                                            <div class="text-danger">{{ form.stop_loss_price.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="field-group">
                                            <label for="{{ form.take_profit_price.id_for_label }}" class="form-label">
                                                Take Profit Price ($) <span class="text-danger">*</span>
                                            </label>
                                            {{ form.take_profit_price }}
                                            <div class="form-text">Price to take profits</div>
                                            {% if form.take_profit_price.errors %}
                                            <div class="text-danger">{{ form.take_profit_price.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Form Errors -->
                                {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {% for error in form.non_field_errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                
                                <!-- Submit Buttons -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{% url 'trading:account_detail' account.id %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="button" class="btn btn-info" onclick="previewBracketOrder()">
                                        <i class="fas fa-eye me-2"></i>Preview
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-layer-group me-2"></i>Create Bracket Order
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Bracket Preview and Info -->
                <div class="col-lg-4">
                    <!-- Bracket Preview -->
                    <div class="card bracket-preview-card mb-4" id="bracketPreview" style="display: none;">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-eye me-2"></i>Bracket Preview
                            </h6>
                            <div id="previewContent">
                                <!-- Preview content will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Account Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="fw-bold">${{ account.current_balance|floatformat:2 }}</div>
                                    <small class="text-muted">Cash Balance</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold">${{ account.equity|floatformat:2 }}</div>
                                    <small class="text-muted">Total Equity</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Bracket Order Benefits -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-star me-2"></i>Bracket Order Benefits
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <small>Automatic risk management</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <small>Predefined profit targets</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <small>Emotion-free trading</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <small>One-click order placement</small>
                                </li>
                                <li>
                                    <i class="fas fa-check text-success me-2"></i>
                                    <small>Consistent risk/reward ratios</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function previewBracketOrder() {
    const symbol = document.getElementById('id_symbol').value;
    const side = document.getElementById('id_side').value;
    const quantity = document.getElementById('id_quantity').value;
    const entryPrice = document.getElementById('id_entry_price').value;
    const stopLoss = document.getElementById('id_stop_loss_price').value;
    const takeProfit = document.getElementById('id_take_profit_price').value;
    
    if (!symbol || !quantity || !entryPrice || !stopLoss || !takeProfit) {
        document.getElementById('bracketPreview').style.display = 'none';
        return;
    }
    
    // Calculate risk and reward
    const entry = parseFloat(entryPrice);
    const stop = parseFloat(stopLoss);
    const profit = parseFloat(takeProfit);
    const qty = parseInt(quantity);
    
    let risk, reward, riskRewardRatio;
    
    if (side === 'buy') {
        risk = (entry - stop) * qty;
        reward = (profit - entry) * qty;
    } else {
        risk = (stop - entry) * qty;
        reward = (entry - profit) * qty;
    }
    
    riskRewardRatio = risk > 0 ? (reward / risk) : 0;
    
    const totalCost = entry * qty + 3.00; // 3 orders × $1 commission each
    
    let previewHtml = `
        <div class="mb-3">
            <strong>${side.toUpperCase()}</strong> ${quantity} shares of <strong>${symbol}</strong>
        </div>
        
        <div class="price-level entry mb-2">
            <div class="fw-bold">Entry Order</div>
            <div>Limit: $${entry.toFixed(2)}</div>
        </div>
        
        <div class="price-level stop-loss mb-2">
            <div class="fw-bold">Stop Loss</div>
            <div>Stop: $${stop.toFixed(2)}</div>
        </div>
        
        <div class="price-level take-profit mb-3">
            <div class="fw-bold">Take Profit</div>
            <div>Limit: $${profit.toFixed(2)}</div>
        </div>
        
        <div class="risk-reward-display">
            <div class="row text-center">
                <div class="col-4">
                    <div class="fw-bold text-danger">Risk</div>
                    <div>$${Math.abs(risk).toFixed(2)}</div>
                </div>
                <div class="col-4">
                    <div class="fw-bold text-success">Reward</div>
                    <div>$${Math.abs(reward).toFixed(2)}</div>
                </div>
                <div class="col-4">
                    <div class="fw-bold">R:R</div>
                    <div>${riskRewardRatio.toFixed(2)}:1</div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <small>Total Cost: $${totalCost.toFixed(2)} (including commissions)</small>
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = previewHtml;
    document.getElementById('bracketPreview').style.display = 'block';
}

// Add event listeners for real-time preview updates
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['id_symbol', 'id_side', 'id_quantity', 'id_entry_price', 'id_stop_loss_price', 'id_take_profit_price'];
    
    inputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener('input', previewBracketOrder);
            element.addEventListener('change', previewBracketOrder);
        }
    });
});
</script>
{% endblock %}
