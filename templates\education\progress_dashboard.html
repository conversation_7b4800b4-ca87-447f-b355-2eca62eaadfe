{% extends 'base.html' %}
{% load static %}

{% block title %}My Learning Progress - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-completed { color: #28a745; }
.stat-in-progress { color: #ffc107; }
.stat-total { color: #007bff; }
.stat-score { color: #6f42c1; }

.progress-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.progress-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.progress-bar-custom {
    height: 10px;
    border-radius: 5px;
    background-color: #e9ecef;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    border-radius: 5px;
    transition: width 0.8s ease;
}

.progress-completed { background: linear-gradient(90deg, #28a745 0%, #20c997 100%); }
.progress-in-progress { background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%); }
.progress-not-started { background: linear-gradient(90deg, #6c757d 0%, #495057 100%); }

.quiz-attempt-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
}

.quiz-passed {
    border-left-color: #28a745;
    background-color: #d4edda;
}

.quiz-failed {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

.achievement-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
    margin: 2px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.section-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-chart-line me-3"></i>My Learning Progress
                </h1>
                <p class="lead mb-0">Track your educational journey and achievements</p>
            </div>
            <div class="col-lg-4 text-center">
                <div class="achievement-badge">
                    <i class="fas fa-trophy me-2"></i>Learning Champion
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number stat-completed">{{ completed_paths }}</div>
            <div class="stat-label">Completed Paths</div>
        </div>
        <div class="stat-card">
            <div class="stat-number stat-in-progress">{{ in_progress_paths }}</div>
            <div class="stat-label">In Progress</div>
        </div>
        <div class="stat-card">
            <div class="stat-number stat-total">{{ total_paths }}</div>
            <div class="stat-label">Total Enrolled</div>
        </div>
        <div class="stat-card">
            <div class="stat-number stat-score">{{ avg_quiz_score }}%</div>
            <div class="stat-label">Avg Quiz Score</div>
        </div>
    </div>

    <div class="row">
        <!-- Learning Paths Progress -->
        <div class="col-lg-8">
            <div class="section-header">
                <h3><i class="fas fa-graduation-cap me-2"></i>Learning Paths Progress</h3>
            </div>
            
            {% for progress in progress_list %}
            <div class="progress-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-2">
                            <a href="{% url 'education:learning_path_detail' progress.learning_path.id %}" 
                               class="text-decoration-none">
                                {{ progress.learning_path.title }}
                            </a>
                        </h5>
                        
                        <p class="text-muted mb-2">{{ progress.learning_path.description|truncatewords:15 }}</p>
                        
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <span class="badge bg-secondary">
                                <i class="fas fa-tag me-1"></i>{{ progress.learning_path.category.name }}
                            </span>
                            <span class="badge bg-info">
                                {{ progress.learning_path.get_difficulty_level_display }}
                            </span>
                            {% if progress.is_completed %}
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Completed
                            </span>
                            {% elif progress.is_started %}
                            <span class="badge bg-warning">
                                <i class="fas fa-play me-1"></i>In Progress
                            </span>
                            {% else %}
                            <span class="badge bg-secondary">
                                <i class="fas fa-pause me-1"></i>Not Started
                            </span>
                            {% endif %}
                        </div>
                        
                        <!-- Progress Bar -->
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Progress</span>
                            <span class="fw-bold">{{ progress.completion_percentage }}%</span>
                        </div>
                        <div class="progress-bar-custom">
                            <div class="progress-fill 
                                {% if progress.is_completed %}progress-completed
                                {% elif progress.is_started %}progress-in-progress
                                {% else %}progress-not-started
                                {% endif %}" 
                                style="width: {{ progress.completion_percentage }}%">
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 text-center">
                        <div class="mb-3">
                            <div class="fw-bold text-primary">{{ progress.total_time_spent }}</div>
                            <small class="text-muted">Minutes Spent</small>
                        </div>
                        
                        {% if progress.average_quiz_score > 0 %}
                        <div class="mb-3">
                            <div class="fw-bold text-success">{{ progress.average_quiz_score|floatformat:1 }}%</div>
                            <small class="text-muted">Quiz Average</small>
                        </div>
                        {% endif %}
                        
                        <div class="d-grid gap-2">
                            {% if progress.is_completed %}
                            <a href="{% url 'education:learning_path_detail' progress.learning_path.id %}" 
                               class="btn btn-outline-success btn-sm">
                                <i class="fas fa-eye me-1"></i>Review
                            </a>
                            {% elif progress.is_started %}
                            <a href="{% url 'education:learning_path_detail' progress.learning_path.id %}" 
                               class="btn btn-primary btn-sm">
                                <i class="fas fa-play me-1"></i>Continue
                            </a>
                            {% else %}
                            <a href="{% url 'education:learning_path_detail' progress.learning_path.id %}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-info me-1"></i>View Details
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fas fa-graduation-cap"></i>
                <h4>No Learning Paths Yet</h4>
                <p class="mb-4">Start your educational journey by enrolling in a learning path.</p>
                <a href="{% url 'education:learning_paths' %}" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>Browse Learning Paths
                </a>
            </div>
            {% endfor %}
        </div>

        <!-- Recent Quiz Attempts -->
        <div class="col-lg-4">
            <div class="section-header">
                <h4><i class="fas fa-question-circle me-2"></i>Recent Quiz Results</h4>
            </div>
            
            {% for attempt in recent_quizzes %}
            <div class="quiz-attempt-card {% if attempt.is_passed %}quiz-passed{% else %}quiz-failed{% endif %}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">{{ attempt.quiz.title }}</h6>
                    <span class="badge {% if attempt.is_passed %}bg-success{% else %}bg-danger{% endif %}">
                        {{ attempt.percentage }}%
                    </span>
                </div>
                
                <div class="small text-muted mb-2">
                    Attempt {{ attempt.attempt_number }} • {{ attempt.completed_at|date:"M d, Y" }}
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <span class="small">
                        {% if attempt.is_passed %}
                        <i class="fas fa-check text-success me-1"></i>Passed
                        {% else %}
                        <i class="fas fa-times text-danger me-1"></i>Failed
                        {% endif %}
                    </span>
                    <a href="{% url 'education:quiz_result' attempt.id %}" 
                       class="btn btn-sm btn-outline-primary">
                        View Results
                    </a>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-4">
                <i class="fas fa-question-circle text-muted" style="font-size: 2rem;"></i>
                <p class="text-muted mt-2">No quiz attempts yet</p>
            </div>
            {% endfor %}
            
            <!-- Quick Actions -->
            <div class="mt-4">
                <h5 class="mb-3">Quick Actions</h5>
                <div class="d-grid gap-2">
                    <a href="{% url 'education:learning_paths' %}" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Browse Learning Paths
                    </a>
                    <a href="{% url 'education:home' %}" class="btn btn-outline-primary">
                        <i class="fas fa-home me-2"></i>Education Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach((bar, index) => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 200 + (index * 100));
    });
    
    // Animate stat numbers
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        if (!isNaN(finalValue)) {
            let currentValue = 0;
            const increment = finalValue / 30;
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    stat.textContent = finalValue + (stat.textContent.includes('%') ? '%' : '');
                    clearInterval(timer);
                } else {
                    stat.textContent = Math.floor(currentValue) + (stat.textContent.includes('%') ? '%' : '');
                }
            }, 50);
        }
    });
});
</script>
{% endblock %}
