from django.urls import path
from . import views

app_name = 'trading'

urlpatterns = [
    # Account management
    path('accounts/', views.account_overview, name='account_overview'),
    path('accounts/create/', views.create_account, name='create_account'),
    path('accounts/<uuid:account_id>/', views.account_detail, name='account_detail'),
    path('accounts/<uuid:account_id>/update/', views.update_account, name='update_account'),
    path('accounts/<uuid:account_id>/close/', views.close_account, name='close_account'),
    
    # Transactions
    path('accounts/<uuid:account_id>/transactions/', views.transaction_history, name='transaction_history'),
    path('deposit/', views.virtual_deposit, name='virtual_deposit'),
    path('deposits/', views.deposit_history, name='deposit_history'),

    # Portfolio management
    path('portfolio/', views.portfolio_overview, name='portfolio_overview'),
    path('portfolio/<uuid:account_id>/', views.portfolio_detail, name='portfolio_detail'),
    path('portfolio/<uuid:account_id>/performance/', views.portfolio_performance, name='portfolio_performance'),
    path('position/<uuid:position_id>/', views.position_detail, name='position_detail'),

    # Order management
    path('order/entry/', views.order_entry, name='order_entry'),
    path('order/entry/<uuid:account_id>/', views.order_entry, name='order_entry_account'),
    path('order/entry/<uuid:account_id>/<str:symbol>/', views.order_entry, name='order_entry_symbol'),
    path('orders/', views.order_list, name='order_list'),
    path('orders/<uuid:account_id>/', views.order_list, name='order_list_account'),
    path('order/<uuid:order_id>/', views.order_detail, name='order_detail'),
    path('order/<uuid:order_id>/cancel/', views.cancel_order, name='cancel_order'),
    path('quick-order/', views.quick_order, name='quick_order'),
    path('trades/', views.trade_history, name='trade_history'),
    path('trades/<uuid:account_id>/', views.trade_history, name='trade_history_account'),

    # Advanced trading features
    path('bracket-order/', views.bracket_order_entry, name='bracket_order_entry'),
    path('bracket-order/<uuid:account_id>/', views.bracket_order_entry, name='bracket_order_entry_account'),
    path('risk-management/', views.risk_management_dashboard, name='risk_management'),
    path('risk-management/<uuid:account_id>/', views.risk_management_dashboard, name='risk_management_account'),
]
