{% extends 'base.html' %}
{% load static %}

{% block title %}Quiz Results: {{ attempt.quiz.title }} - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
.result-header {
    background: {% if attempt.is_passed %}linear-gradient(135deg, #28a745 0%, #20c997 100%){% else %}linear-gradient(135deg, #dc3545 0%, #fd7e14 100%){% endif %};
    color: white;
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 30px;
    text-align: center;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.score-number {
    font-size: 2.5rem;
    font-weight: bold;
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.stat-correct { color: #28a745; }
.stat-incorrect { color: #dc3545; }
.stat-score { color: #007bff; }
.stat-time { color: #6f42c1; }

.question-review {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.question-correct {
    border-left: 4px solid #28a745;
}

.question-incorrect {
    border-left: 4px solid #dc3545;
}

.question-number {
    background: #007bff;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 15px;
}

.answer-option {
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 8px;
    border: 1px solid #e9ecef;
}

.answer-correct {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.answer-incorrect {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.answer-selected {
    border-width: 2px;
    font-weight: bold;
}

.explanation-box {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.achievement-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 20px;
}

.retry-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    margin-top: 30px;
}

.navigation-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 30px;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Result Header -->
    <div class="result-header">
        <div class="score-circle">
            <div class="score-number">{{ attempt.percentage }}%</div>
        </div>
        
        <h1 class="display-5 fw-bold mb-3">
            {% if attempt.is_passed %}
                <i class="fas fa-trophy me-3"></i>Congratulations!
            {% else %}
                <i class="fas fa-redo me-3"></i>Keep Learning!
            {% endif %}
        </h1>
        
        <p class="lead mb-3">
            {% if attempt.is_passed %}
                You passed the quiz with a score of {{ attempt.percentage }}%
            {% else %}
                You scored {{ attempt.percentage }}%. The passing score is {{ attempt.quiz.passing_score }}%
            {% endif %}
        </p>
        
        {% if attempt.is_passed %}
        <div class="achievement-badge">
            <i class="fas fa-medal me-2"></i>Quiz Completed Successfully!
        </div>
        {% endif %}
    </div>

    <!-- Statistics -->
    <div class="result-stats">
        <div class="stat-card">
            <div class="stat-number stat-score">{{ attempt.score|floatformat:1 }}/{{ attempt.max_score|floatformat:1 }}</div>
            <div class="stat-label">Points Earned</div>
        </div>
        <div class="stat-card">
            <div class="stat-number stat-correct">{{ answers|length }}</div>
            <div class="stat-label">Questions Answered</div>
        </div>
        <div class="stat-card">
            <div class="stat-number stat-correct">
                {% with correct_count=answers|length %}
                    {% for answer in answers %}
                        {% if answer.is_correct %}{{ forloop.counter0|add:1 }}{% endif %}
                    {% endfor %}
                {% endwith %}
            </div>
            <div class="stat-label">Correct Answers</div>
        </div>
        {% if attempt.time_taken %}
        <div class="stat-card">
            <div class="stat-number stat-time">{{ attempt.time_taken|floatformat:0 }}s</div>
            <div class="stat-label">Time Taken</div>
        </div>
        {% endif %}
    </div>

    <!-- Question Review -->
    {% if attempt.quiz.show_correct_answers %}
    <div class="row">
        <div class="col-12">
            <h3 class="mb-4">
                <i class="fas fa-list-alt me-2"></i>Question Review
            </h3>
            
            {% for answer in answers %}
            <div class="question-review {% if answer.is_correct %}question-correct{% else %}question-incorrect{% endif %}">
                <div class="question-number">{{ forloop.counter }}</div>
                
                <h5 class="mb-3">{{ answer.question.question_text }}</h5>
                
                {% if answer.question.question_type == 'multiple_choice' or answer.question.question_type == 'true_false' %}
                    {% for choice in answer.question.choices.all %}
                    <div class="answer-option 
                        {% if choice.is_correct %}answer-correct{% endif %}
                        {% if choice == answer.selected_choice %}answer-selected{% endif %}
                        {% if choice == answer.selected_choice and not choice.is_correct %}answer-incorrect{% endif %}">
                        
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                {% if choice.is_correct %}
                                    <i class="fas fa-check-circle text-success"></i>
                                {% elif choice == answer.selected_choice and not choice.is_correct %}
                                    <i class="fas fa-times-circle text-danger"></i>
                                {% else %}
                                    <i class="far fa-circle text-muted"></i>
                                {% endif %}
                            </div>
                            
                            <div class="flex-grow-1">
                                {{ choice.choice_text }}
                                {% if choice == answer.selected_choice %}
                                    <span class="badge bg-primary ms-2">Your Answer</span>
                                {% endif %}
                                {% if choice.is_correct %}
                                    <span class="badge bg-success ms-2">Correct</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                
                {% elif answer.question.question_type == 'short_answer' %}
                    <div class="answer-option answer-selected">
                        <strong>Your Answer:</strong> {{ answer.text_answer|default:"No answer provided" }}
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Short answer questions require manual grading by your instructor.
                        </small>
                    </div>
                
                {% elif answer.question.question_type == 'numerical' %}
                    <div class="answer-option answer-selected">
                        <strong>Your Answer:</strong> {{ answer.numerical_answer|default:"No answer provided" }}
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Numerical questions require manual grading by your instructor.
                        </small>
                    </div>
                {% endif %}
                
                {% if answer.question.explanation %}
                <div class="explanation-box">
                    <h6><i class="fas fa-lightbulb me-2"></i>Explanation</h6>
                    <p class="mb-0">{{ answer.question.explanation }}</p>
                </div>
                {% endif %}
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-star me-1"></i>Worth {{ answer.question.points }} point{{ answer.question.points|pluralize }}
                        {% if answer.is_correct %}
                            • <span class="text-success">Earned {{ answer.question.points }} point{{ answer.question.points|pluralize }}</span>
                        {% else %}
                            • <span class="text-danger">Earned 0 points</span>
                        {% endif %}
                    </small>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Retry Section -->
    {% if not attempt.is_passed %}
    <div class="retry-section">
        <h4 class="mb-3">
            <i class="fas fa-redo me-2"></i>Want to Try Again?
        </h4>
        <p class="mb-4">
            You have {{ attempt.quiz.max_attempts|add:"-1"|add:attempt.attempt_number|default:0 }} attempt{{ attempt.quiz.max_attempts|add:"-1"|add:attempt.attempt_number|pluralize }} remaining.
            Review the material and try again when you're ready.
        </p>
        
        {% if attempt.attempt_number < attempt.quiz.max_attempts %}
        <a href="{% url 'education:quiz_detail' attempt.quiz.id %}" class="btn btn-primary btn-lg me-3">
            <i class="fas fa-redo me-2"></i>Retake Quiz
        </a>
        {% endif %}
        
        {% if attempt.quiz.tutorial %}
        <a href="{% url 'education:tutorial_detail' attempt.quiz.tutorial.id %}" class="btn btn-outline-primary btn-lg">
            <i class="fas fa-book me-2"></i>Review Tutorial
        </a>
        {% endif %}
    </div>
    {% endif %}

    <!-- Navigation -->
    <div class="navigation-section">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-3">What's Next?</h5>
                <div class="d-flex flex-wrap gap-2">
                    {% if attempt.quiz.tutorial %}
                    <a href="{% url 'education:tutorial_detail' attempt.quiz.tutorial.id %}" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Tutorial
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'education:quiz_detail' attempt.quiz.id %}" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-info me-2"></i>Quiz Details
                    </a>
                </div>
            </div>
            
            <div class="col-md-6 text-end">
                <a href="{% url 'education:progress_dashboard' %}" class="btn btn-primary">
                    <i class="fas fa-chart-line me-2"></i>View Progress
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate score circle
    const scoreCircle = document.querySelector('.score-circle');
    if (scoreCircle) {
        scoreCircle.style.transform = 'scale(0)';
        setTimeout(() => {
            scoreCircle.style.transition = 'transform 0.5s ease-out';
            scoreCircle.style.transform = 'scale(1)';
        }, 200);
    }
    
    // Animate stat numbers
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach((stat, index) => {
        const finalValue = parseFloat(stat.textContent);
        if (!isNaN(finalValue)) {
            let currentValue = 0;
            const increment = finalValue / 30;
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    stat.textContent = finalValue + (stat.textContent.includes('%') ? '%' : stat.textContent.includes('s') ? 's' : '');
                    clearInterval(timer);
                } else {
                    const displayValue = stat.textContent.includes('.') ? currentValue.toFixed(1) : Math.floor(currentValue);
                    stat.textContent = displayValue + (stat.textContent.includes('%') ? '%' : stat.textContent.includes('s') ? 's' : '');
                }
            }, 50);
        }
    });
});
</script>
{% endblock %}
