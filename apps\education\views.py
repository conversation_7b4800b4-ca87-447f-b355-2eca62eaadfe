"""
Education views for the trading simulator.
"""

import json
import logging
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg, Sum
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt

from .models import (
    Category, LearningPath, Tutorial, TutorialStep, Quiz, QuizQuestion,
    QuizChoice, UserProgress, TutorialProgress, QuizAttempt, QuizAnswer
)

logger = logging.getLogger(__name__)


@login_required
def education_home(request):
    """Education home page with learning paths overview."""
    # Get featured learning paths
    featured_paths = LearningPath.objects.filter(
        is_published=True,
        is_featured=True
    ).select_related('category').order_by('order')[:6]

    # Get categories with learning path counts
    categories = Category.objects.filter(
        is_active=True
    ).annotate(
        path_count=Count('learning_paths', filter=Q(learning_paths__is_published=True))
    ).order_by('order')

    # Get user's recent progress
    recent_progress = UserProgress.objects.filter(
        user=request.user,
        is_started=True
    ).select_related('learning_path').order_by('-last_accessed')[:5]

    # Get user's achievements
    completed_paths = UserProgress.objects.filter(
        user=request.user,
        is_completed=True
    ).count()

    total_time = UserProgress.objects.filter(
        user=request.user
    ).aggregate(total=Sum('total_time_spent'))['total'] or 0

    context = {
        'featured_paths': featured_paths,
        'categories': categories,
        'recent_progress': recent_progress,
        'completed_paths': completed_paths,
        'total_time': total_time,
        'title': 'Trading Education Center'
    }

    return render(request, 'education/home.html', context)


@login_required
def learning_paths(request):
    """List all learning paths with filtering and search."""
    paths = LearningPath.objects.filter(is_published=True).select_related('category')

    # Filtering
    category_id = request.GET.get('category')
    difficulty = request.GET.get('difficulty')
    search = request.GET.get('search')

    if category_id:
        paths = paths.filter(category_id=category_id)

    if difficulty:
        paths = paths.filter(difficulty_level=difficulty)

    if search:
        paths = paths.filter(
            Q(title__icontains=search) |
            Q(description__icontains=search) |
            Q(tags__icontains=search)
        )

    # Pagination
    paginator = Paginator(paths.order_by('order', 'title'), 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get categories for filter
    categories = Category.objects.filter(is_active=True).order_by('order')

    # Get current category object if category_id is provided
    current_category_obj = None
    if category_id:
        try:
            current_category_obj = Category.objects.get(id=category_id, is_active=True)
        except Category.DoesNotExist:
            pass

    context = {
        'page_obj': page_obj,
        'categories': categories,
        'current_category': category_id,
        'current_category_obj': current_category_obj,
        'current_difficulty': difficulty,
        'search_query': search,
        'title': 'Learning Paths'
    }

    return render(request, 'education/learning_paths.html', context)


@login_required
def learning_path_detail(request, path_id):
    """Learning path detail page with tutorials and progress."""
    path = get_object_or_404(
        LearningPath.objects.select_related('category'),
        id=path_id,
        is_published=True
    )

    # Get tutorials in order
    tutorials = path.tutorials.filter(is_published=True).order_by('order')

    # Get or create user progress
    user_progress, created = UserProgress.objects.get_or_create(
        user=request.user,
        learning_path=path,
        defaults={'is_started': False}
    )

    # Get tutorial progress for each tutorial
    tutorial_progress = {}
    if user_progress.is_started:
        progress_qs = TutorialProgress.objects.filter(
            user=request.user,
            tutorial__in=tutorials
        ).select_related('tutorial')

        for progress in progress_qs:
            tutorial_progress[progress.tutorial.id] = progress

    # Check prerequisites
    can_start = True
    missing_prerequisites = []

    for prereq in path.prerequisites.all():
        prereq_progress = UserProgress.objects.filter(
            user=request.user,
            learning_path=prereq,
            is_completed=True
        ).first()

        if not prereq_progress:
            can_start = False
            missing_prerequisites.append(prereq)

    context = {
        'path': path,
        'tutorials': tutorials,
        'user_progress': user_progress,
        'tutorial_progress': tutorial_progress,
        'can_start': can_start,
        'missing_prerequisites': missing_prerequisites,
        'title': path.title
    }

    return render(request, 'education/learning_path_detail.html', context)


@login_required
def start_learning_path(request, path_id):
    """Start a learning path."""
    if request.method == 'POST':
        path = get_object_or_404(LearningPath, id=path_id, is_published=True)

        # Get or create user progress
        user_progress, created = UserProgress.objects.get_or_create(
            user=request.user,
            learning_path=path
        )

        # Start the learning path
        user_progress.start_learning()

        messages.success(request, f'Started learning path: {path.title}')
        return redirect('education:learning_path_detail', path_id=path.id)

    return redirect('education:learning_paths')


@login_required
def tutorial_detail(request, tutorial_id):
    """Tutorial detail page with content and steps."""
    tutorial = get_object_or_404(
        Tutorial.objects.select_related('learning_path'),
        id=tutorial_id,
        is_published=True
    )

    # Check if user has access to this tutorial
    user_progress = UserProgress.objects.filter(
        user=request.user,
        learning_path=tutorial.learning_path,
        is_started=True
    ).first()

    if not user_progress:
        messages.error(request, 'You need to start the learning path first.')
        return redirect('education:learning_path_detail', path_id=tutorial.learning_path.id)

    # Get or create tutorial progress
    tutorial_progress, created = TutorialProgress.objects.get_or_create(
        user=request.user,
        tutorial=tutorial
    )

    # Start tutorial if not started
    if not tutorial_progress.is_started:
        tutorial_progress.start_tutorial()

    # Get tutorial steps
    steps = tutorial.steps.all().order_by('order')

    # Get completed steps
    completed_steps = tutorial_progress.completed_steps.all()
    completed_step_ids = [step.id for step in completed_steps]

    # Get current step
    current_step = tutorial_progress.current_step
    if not current_step and steps.exists():
        current_step = steps.first()
        tutorial_progress.current_step = current_step
        tutorial_progress.save()

    # Get quiz if exists
    quiz = tutorial.quizzes.filter(is_published=True).first()
    quiz_attempts = []
    if quiz:
        quiz_attempts = QuizAttempt.objects.filter(
            user=request.user,
            quiz=quiz
        ).order_by('-created_at')

    context = {
        'tutorial': tutorial,
        'tutorial_progress': tutorial_progress,
        'steps': steps,
        'completed_step_ids': completed_step_ids,
        'current_step': current_step,
        'quiz': quiz,
        'quiz_attempts': quiz_attempts,
        'title': tutorial.title
    }

    return render(request, 'education/tutorial_detail.html', context)


@login_required
def complete_tutorial_step(request, step_id):
    """Mark a tutorial step as completed."""
    if request.method == 'POST':
        step = get_object_or_404(TutorialStep, id=step_id)

        # Get tutorial progress
        tutorial_progress = TutorialProgress.objects.filter(
            user=request.user,
            tutorial=step.tutorial
        ).first()

        if tutorial_progress:
            # Complete the step
            tutorial_progress.complete_step(step)

            # Move to next step
            next_steps = step.tutorial.steps.filter(order__gt=step.order).order_by('order')
            if next_steps.exists():
                tutorial_progress.current_step = next_steps.first()
                tutorial_progress.save()

            return JsonResponse({
                'success': True,
                'message': 'Step completed successfully',
                'completion_percentage': tutorial_progress.completion_percentage
            })

    return JsonResponse({'success': False, 'message': 'Invalid request'})


@login_required
def quiz_detail(request, quiz_id):
    """Quiz detail page."""
    quiz = get_object_or_404(Quiz, id=quiz_id, is_published=True)

    # Check user access
    if quiz.tutorial:
        tutorial_progress = TutorialProgress.objects.filter(
            user=request.user,
            tutorial=quiz.tutorial,
            is_started=True
        ).first()

        if not tutorial_progress:
            messages.error(request, 'You need to start the tutorial first.')
            return redirect('education:tutorial_detail', tutorial_id=quiz.tutorial.id)

    # Get user's attempts
    attempts = QuizAttempt.objects.filter(
        user=request.user,
        quiz=quiz
    ).order_by('-created_at')

    # Check if user can take quiz
    can_take_quiz = True
    if attempts.count() >= quiz.max_attempts:
        can_take_quiz = False

    # Get best attempt
    best_attempt = attempts.filter(is_completed=True).order_by('-percentage').first()

    context = {
        'quiz': quiz,
        'attempts': attempts,
        'can_take_quiz': can_take_quiz,
        'best_attempt': best_attempt,
        'title': quiz.title
    }

    return render(request, 'education/quiz_detail.html', context)


@login_required
def take_quiz(request, quiz_id):
    """Take a quiz."""
    quiz = get_object_or_404(Quiz, id=quiz_id, is_published=True)

    # Check if user can take quiz
    attempts_count = QuizAttempt.objects.filter(user=request.user, quiz=quiz).count()
    if attempts_count >= quiz.max_attempts:
        messages.error(request, 'You have reached the maximum number of attempts for this quiz.')
        return redirect('education:quiz_detail', quiz_id=quiz.id)

    if request.method == 'POST':
        # Process quiz submission
        return process_quiz_submission(request, quiz)

    # Get questions
    questions = quiz.questions.all().order_by('order')
    if quiz.randomize_questions:
        questions = questions.order_by('?')

    # Create new attempt
    attempt_number = attempts_count + 1
    attempt = QuizAttempt.objects.create(
        user=request.user,
        quiz=quiz,
        attempt_number=attempt_number
    )

    context = {
        'quiz': quiz,
        'questions': questions,
        'attempt': attempt,
        'title': f'Take Quiz: {quiz.title}'
    }

    return render(request, 'education/take_quiz.html', context)


def process_quiz_submission(request, quiz):
    """Process quiz submission and calculate score."""
    attempt_id = request.POST.get('attempt_id')
    attempt = get_object_or_404(QuizAttempt, id=attempt_id, user=request.user)

    # Process answers
    for question in quiz.questions.all():
        answer_key = f'question_{question.id}'

        if question.question_type == 'multiple_choice':
            choice_id = request.POST.get(answer_key)
            if choice_id:
                choice = get_object_or_404(QuizChoice, id=choice_id, question=question)
                QuizAnswer.objects.create(
                    attempt=attempt,
                    question=question,
                    selected_choice=choice,
                    is_correct=choice.is_correct
                )

        elif question.question_type == 'true_false':
            choice_id = request.POST.get(answer_key)
            if choice_id:
                choice = get_object_or_404(QuizChoice, id=choice_id, question=question)
                QuizAnswer.objects.create(
                    attempt=attempt,
                    question=question,
                    selected_choice=choice,
                    is_correct=choice.is_correct
                )

        elif question.question_type == 'short_answer':
            text_answer = request.POST.get(answer_key, '').strip()
            if text_answer:
                # For short answer, we'll need manual grading or keyword matching
                # For now, mark as incorrect and require manual review
                QuizAnswer.objects.create(
                    attempt=attempt,
                    question=question,
                    text_answer=text_answer,
                    is_correct=False  # Requires manual grading
                )

        elif question.question_type == 'numerical':
            numerical_answer = request.POST.get(answer_key)
            if numerical_answer:
                try:
                    numerical_value = float(numerical_answer)
                    # For numerical answers, we'd need to define acceptable ranges
                    # For now, mark as incorrect and require manual review
                    QuizAnswer.objects.create(
                        attempt=attempt,
                        question=question,
                        numerical_answer=numerical_value,
                        is_correct=False  # Requires manual grading
                    )
                except ValueError:
                    pass

    # Calculate score and complete attempt
    attempt.calculate_score()
    attempt.is_completed = True
    attempt.completed_at = timezone.now()
    attempt.save()

    # Update tutorial progress if quiz is part of tutorial
    if quiz.tutorial:
        tutorial_progress = TutorialProgress.objects.filter(
            user=request.user,
            tutorial=quiz.tutorial
        ).first()

        if tutorial_progress:
            tutorial_progress.quiz_score = attempt.percentage
            tutorial_progress.save()

            # If quiz is passed, mark tutorial as completed
            if attempt.is_passed:
                tutorial_progress.is_completed = True
                tutorial_progress.completed_at = timezone.now()
                tutorial_progress.save()

                # Update learning path progress
                user_progress = UserProgress.objects.filter(
                    user=request.user,
                    learning_path=quiz.tutorial.learning_path
                ).first()

                if user_progress:
                    user_progress.complete_tutorial(quiz.tutorial)

    messages.success(request, f'Quiz completed! Your score: {attempt.percentage}%')
    return redirect('education:quiz_result', attempt_id=attempt.id)


@login_required
def quiz_result(request, attempt_id):
    """Show quiz results."""
    attempt = get_object_or_404(
        QuizAttempt.objects.select_related('quiz'),
        id=attempt_id,
        user=request.user
    )

    # Get answers with questions and choices
    answers = attempt.answers.select_related(
        'question', 'selected_choice'
    ).order_by('question__order')

    context = {
        'attempt': attempt,
        'answers': answers,
        'title': f'Quiz Results: {attempt.quiz.title}'
    }

    return render(request, 'education/quiz_result.html', context)


@login_required
def user_progress_dashboard(request):
    """User's learning progress dashboard."""
    # Get all user progress
    progress_list = UserProgress.objects.filter(
        user=request.user
    ).select_related('learning_path', 'current_tutorial').order_by('-last_accessed')

    # Get statistics
    total_paths = progress_list.count()
    completed_paths = progress_list.filter(is_completed=True).count()
    in_progress_paths = progress_list.filter(is_started=True, is_completed=False).count()

    # Get recent quiz attempts
    recent_quizzes = QuizAttempt.objects.filter(
        user=request.user,
        is_completed=True
    ).select_related('quiz').order_by('-completed_at')[:10]

    # Calculate average quiz score
    avg_quiz_score = QuizAttempt.objects.filter(
        user=request.user,
        is_completed=True
    ).aggregate(avg_score=Avg('percentage'))['avg_score'] or 0

    context = {
        'progress_list': progress_list,
        'total_paths': total_paths,
        'completed_paths': completed_paths,
        'in_progress_paths': in_progress_paths,
        'recent_quizzes': recent_quizzes,
        'avg_quiz_score': round(avg_quiz_score, 1),
        'title': 'My Learning Progress'
    }

    return render(request, 'education/progress_dashboard.html', context)
