{% extends 'base.html' %}
{% load static %}

{% block title %}Place Order - {{ account.name }} - Trading Simulator{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item">
    <a href="{% url 'trading:account_overview' %}" class="text-decoration-none">
        <i class="fas fa-wallet me-1"></i>Trading
    </a>
</li>
<li class="breadcrumb-item">
    <a href="{% url 'trading:account_detail' account.id %}" class="text-decoration-none">
        {{ account.name }}
    </a>
</li>
<li class="breadcrumb-item active" aria-current="page">
    Place Order
</li>
{% endblock %}

{% block extra_css %}
<style>
    .order-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    
    .order-form-card {
        border-left: 4px solid #667eea;
    }
    
    .order-preview-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 15px;
    }
    
    .price-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
    }
    
    .risk-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1rem;
    }
    
    .order-type-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }
    
    .field-group {
        margin-bottom: 1.5rem;
    }
    
    .symbol-info {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 col-lg-2 dashboard-sidebar p-0">
            <div class="p-3">
                <div class="sidebar-section-title">Trading</div>
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="{% url 'trading:account_overview' %}" class="nav-link">
                            <i class="fas fa-wallet"></i>
                            Accounts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:order_entry' %}" class="nav-link active">
                            <i class="fas fa-plus-circle"></i>
                            Place Order
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:bracket_order_entry' %}" class="nav-link">
                            <i class="fas fa-layer-group"></i>
                            Bracket Order
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:order_list' %}" class="nav-link">
                            <i class="fas fa-list-alt"></i>
                            Orders
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:risk_management' %}" class="nav-link">
                            <i class="fas fa-shield-alt"></i>
                            Risk Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:trade_history' %}" class="nav-link">
                            <i class="fas fa-history"></i>
                            Trades
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:portfolio_overview' %}" class="nav-link">
                            <i class="fas fa-chart-pie"></i>
                            Portfolio
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 p-4">
            <!-- Order Header -->
            <div class="order-header p-4 mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1><i class="fas fa-plus-circle me-3"></i>Place Order</h1>
                        <p class="mb-0">Account: {{ account.name }} • Balance: ${{ account.current_balance|floatformat:2 }}</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="{% url 'trading:order_list_account' account.id %}" class="btn btn-light">
                            <i class="fas fa-list me-2"></i>View Orders
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Order Form -->
                <div class="col-lg-8">
                    <div class="card order-form-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Order Details
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if symbol_obj %}
                            <div class="symbol-info">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h6 class="mb-1">{{ symbol_obj.symbol }} - {{ symbol_obj.name }}</h6>
                                        <small class="text-muted">
                                            {{ symbol_obj.exchange.code }} • {{ symbol_obj.sector.name|default:"N/A" }}
                                        </small>
                                    </div>
                                    <div class="col-md-4 text-md-end">
                                        {% if current_price %}
                                        <div class="fw-bold">${{ current_price|floatformat:2 }}</div>
                                        <small class="text-muted">Current Price</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            
                            <form method="post" id="orderForm">
                                {% csrf_token %}
                                
                                <!-- Symbol Search -->
                                <div class="field-group">
                                    <label for="{{ form.symbol_search.id_for_label }}" class="form-label">
                                        Stock Symbol
                                    </label>
                                    {{ form.symbol_search }}
                                    {% if form.symbol_search.help_text %}
                                    <div class="form-text">{{ form.symbol_search.help_text }}</div>
                                    {% endif %}
                                    {% if form.symbol_search.errors %}
                                    <div class="text-danger">{{ form.symbol_search.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                {{ form.symbol }}
                                
                                <!-- Order Type and Side -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="field-group">
                                            <label for="{{ form.order_type.id_for_label }}" class="form-label">
                                                Order Type
                                            </label>
                                            {{ form.order_type }}
                                            <div class="order-type-help">
                                                <strong>Market:</strong> Execute immediately at current price<br>
                                                <strong>Limit:</strong> Execute only at specified price or better<br>
                                                <strong>Stop:</strong> Trigger market order when stop price reached
                                            </div>
                                            {% if form.order_type.errors %}
                                            <div class="text-danger">{{ form.order_type.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="field-group">
                                            <label for="{{ form.side.id_for_label }}" class="form-label">
                                                Order Side
                                            </label>
                                            {{ form.side }}
                                            {% if form.side.errors %}
                                            <div class="text-danger">{{ form.side.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Quantity -->
                                <div class="field-group">
                                    <label for="{{ form.quantity.id_for_label }}" class="form-label">
                                        Quantity (Shares)
                                    </label>
                                    {{ form.quantity }}
                                    {% if form.quantity.help_text %}
                                    <div class="form-text">{{ form.quantity.help_text }}</div>
                                    {% endif %}
                                    {% if form.quantity.errors %}
                                    <div class="text-danger">{{ form.quantity.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <!-- Price Fields -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="field-group">
                                            <label for="{{ form.price.id_for_label }}" class="form-label">
                                                Limit Price ($)
                                            </label>
                                            {{ form.price }}
                                            {% if form.price.help_text %}
                                            <div class="form-text">{{ form.price.help_text }}</div>
                                            {% endif %}
                                            {% if form.price.errors %}
                                            <div class="text-danger">{{ form.price.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="field-group">
                                            <label for="{{ form.stop_price.id_for_label }}" class="form-label">
                                                Stop Price ($)
                                            </label>
                                            {{ form.stop_price }}
                                            {% if form.stop_price.help_text %}
                                            <div class="form-text">{{ form.stop_price.help_text }}</div>
                                            {% endif %}
                                            {% if form.stop_price.errors %}
                                            <div class="text-danger">{{ form.stop_price.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Time in Force -->
                                <div class="field-group">
                                    <label for="{{ form.time_in_force.id_for_label }}" class="form-label">
                                        Time in Force
                                    </label>
                                    {{ form.time_in_force }}
                                    <div class="form-text">
                                        <strong>Day:</strong> Order expires at end of trading day<br>
                                        <strong>GTC:</strong> Good Till Cancelled - remains active until filled or cancelled
                                    </div>
                                    {% if form.time_in_force.errors %}
                                    <div class="text-danger">{{ form.time_in_force.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <!-- Notes -->
                                <div class="field-group">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">
                                        Notes (Optional)
                                    </label>
                                    {{ form.notes }}
                                    {% if form.notes.errors %}
                                    <div class="text-danger">{{ form.notes.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <!-- Submit Buttons -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{% url 'trading:account_detail' account.id %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="button" class="btn btn-info" onclick="previewOrder()">
                                        <i class="fas fa-eye me-2"></i>Preview Order
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-check me-2"></i>Place Order
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Order Preview and Info -->
                <div class="col-lg-4">
                    <!-- Order Preview -->
                    <div class="card order-preview-card mb-4" id="orderPreview" style="display: none;">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-eye me-2"></i>Order Preview
                            </h6>
                            <div id="previewContent">
                                <!-- Preview content will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Account Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="fw-bold">${{ account.current_balance|floatformat:2 }}</div>
                                    <small class="text-muted">Cash Balance</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold">${{ account.equity|floatformat:2 }}</div>
                                    <small class="text-muted">Total Equity</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Trading Tips -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>Trading Tips
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <small>Use limit orders to control execution price</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <small>Set stop-losses to manage risk</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <small>Review order details before submitting</small>
                                </li>
                                <li>
                                    <i class="fas fa-check text-success me-2"></i>
                                    <small>Monitor your positions regularly</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updatePriceFields() {
    const orderType = document.getElementById('id_order_type').value;
    const priceField = document.getElementById('id_price').parentElement;
    const stopPriceField = document.getElementById('id_stop_price').parentElement;
    
    // Show/hide price fields based on order type
    if (orderType === 'market') {
        priceField.style.display = 'none';
        stopPriceField.style.display = 'none';
    } else if (orderType === 'limit') {
        priceField.style.display = 'block';
        stopPriceField.style.display = 'none';
    } else if (orderType === 'stop') {
        priceField.style.display = 'none';
        stopPriceField.style.display = 'block';
    } else if (orderType === 'stop_limit') {
        priceField.style.display = 'block';
        stopPriceField.style.display = 'block';
    }
    
    updateOrderPreview();
}

function updateOrderPreview() {
    const symbol = document.getElementById('id_symbol_search').value;
    const orderType = document.getElementById('id_order_type').value;
    const side = document.getElementById('id_side').value;
    const quantity = document.getElementById('id_quantity').value;
    const price = document.getElementById('id_price').value;
    const stopPrice = document.getElementById('id_stop_price').value;
    
    if (!symbol || !quantity || quantity <= 0) {
        document.getElementById('orderPreview').style.display = 'none';
        return;
    }
    
    let previewHtml = `
        <div class="mb-2">
            <strong>${side.toUpperCase()}</strong> ${quantity} shares of <strong>${symbol.split(' ')[0]}</strong>
        </div>
        <div class="mb-2">
            Order Type: <strong>${orderType.replace('_', ' ').toUpperCase()}</strong>
        </div>
    `;
    
    if (orderType === 'limit' && price) {
        previewHtml += `<div class="mb-2">Limit Price: <strong>$${parseFloat(price).toFixed(2)}</strong></div>`;
        const estimatedCost = parseFloat(quantity) * parseFloat(price) + 1.00; // Add commission
        previewHtml += `<div class="mb-2">Estimated Cost: <strong>$${estimatedCost.toFixed(2)}</strong></div>`;
    } else if (orderType === 'stop' && stopPrice) {
        previewHtml += `<div class="mb-2">Stop Price: <strong>$${parseFloat(stopPrice).toFixed(2)}</strong></div>`;
    } else if (orderType === 'stop_limit' && price && stopPrice) {
        previewHtml += `<div class="mb-2">Limit Price: <strong>$${parseFloat(price).toFixed(2)}</strong></div>`;
        previewHtml += `<div class="mb-2">Stop Price: <strong>$${parseFloat(stopPrice).toFixed(2)}</strong></div>`;
    }
    
    previewHtml += `<div class="mt-2"><small>Commission: $1.00</small></div>`;
    
    document.getElementById('previewContent').innerHTML = previewHtml;
    document.getElementById('orderPreview').style.display = 'block';
}

function previewOrder() {
    updateOrderPreview();
    if (document.getElementById('orderPreview').style.display === 'block') {
        document.getElementById('orderPreview').scrollIntoView({ behavior: 'smooth' });
    }
}

// Initialize form
document.addEventListener('DOMContentLoaded', function() {
    updatePriceFields();
    
    // Add event listeners
    document.getElementById('id_order_type').addEventListener('change', updatePriceFields);
    document.getElementById('id_side').addEventListener('change', updateOrderPreview);
    document.getElementById('id_quantity').addEventListener('input', updateOrderPreview);
    document.getElementById('id_price').addEventListener('input', updateOrderPreview);
    document.getElementById('id_stop_price').addEventListener('input', updateOrderPreview);
    document.getElementById('id_symbol_search').addEventListener('input', updateOrderPreview);
});
</script>
{% endblock %}
