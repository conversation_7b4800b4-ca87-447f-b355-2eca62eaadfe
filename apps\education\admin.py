"""
Admin configuration for education models.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Category, LearningPath, Tutorial, TutorialStep, Quiz, QuizQuestion,
    QuizChoice, UserProgress, TutorialProgress, QuizAttempt, QuizAnswer
)


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'order', 'is_active', 'learning_paths_count', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['order', 'name']

    def learning_paths_count(self, obj):
        return obj.learning_paths.count()
    learning_paths_count.short_description = 'Learning Paths'


class TutorialInline(admin.TabularInline):
    model = Tutorial
    extra = 0
    fields = ['title', 'order', 'difficulty_level', 'estimated_duration', 'is_published']
    readonly_fields = ['created_at']


@admin.register(LearningPath)
class LearningPathAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'difficulty_level', 'total_lessons', 'is_published', 'is_featured', 'created_at']
    list_filter = ['difficulty_level', 'is_published', 'is_featured', 'category', 'created_at']
    search_fields = ['title', 'description', 'tags']
    filter_horizontal = ['prerequisites']
    inlines = [TutorialInline]
    ordering = ['order', 'title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'category', 'difficulty_level')
        }),
        ('Content', {
            'fields': ('estimated_duration', 'thumbnail', 'banner_image', 'tags')
        }),
        ('Prerequisites', {
            'fields': ('prerequisites',)
        }),
        ('Status', {
            'fields': ('is_published', 'is_featured', 'order')
        }),
        ('Metadata', {
            'fields': ('created_by',),
            'classes': ('collapse',)
        })
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class TutorialStepInline(admin.TabularInline):
    model = TutorialStep
    extra = 0
    fields = ['title', 'step_type', 'order', 'is_optional']


class QuizInline(admin.TabularInline):
    model = Quiz
    extra = 0
    fields = ['title', 'passing_score', 'max_attempts', 'is_published']


@admin.register(Tutorial)
class TutorialAdmin(admin.ModelAdmin):
    list_display = ['title', 'learning_path', 'content_type', 'difficulty_level', 'order', 'is_published', 'completion_rate_display']
    list_filter = ['content_type', 'difficulty_level', 'is_published', 'is_interactive', 'learning_path']
    search_fields = ['title', 'description', 'content']
    filter_horizontal = ['prerequisites']
    inlines = [TutorialStepInline, QuizInline]
    ordering = ['learning_path', 'order']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'learning_path', 'content_type', 'difficulty_level')
        }),
        ('Content', {
            'fields': ('content', 'summary', 'learning_objectives', 'thumbnail', 'video_url')
        }),
        ('Structure', {
            'fields': ('order', 'estimated_duration', 'prerequisites')
        }),
        ('Requirements', {
            'fields': ('required_score',)
        }),
        ('Status', {
            'fields': ('is_published', 'is_interactive')
        }),
        ('Metadata', {
            'fields': ('created_by',),
            'classes': ('collapse',)
        })
    )

    def completion_rate_display(self, obj):
        rate = obj.completion_rate
        color = 'green' if rate >= 70 else 'orange' if rate >= 40 else 'red'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, rate
        )
    completion_rate_display.short_description = 'Completion Rate'

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(TutorialStep)
class TutorialStepAdmin(admin.ModelAdmin):
    list_display = ['title', 'tutorial', 'step_type', 'order', 'is_optional']
    list_filter = ['step_type', 'is_optional', 'tutorial__learning_path']
    search_fields = ['title', 'content', 'tutorial__title']
    ordering = ['tutorial', 'order']


class QuizQuestionInline(admin.TabularInline):
    model = QuizQuestion
    extra = 0
    fields = ['question_text', 'question_type', 'points', 'order']


@admin.register(Quiz)
class QuizAdmin(admin.ModelAdmin):
    list_display = ['title', 'tutorial', 'learning_path', 'total_questions', 'passing_score', 'average_score_display', 'is_published']
    list_filter = ['is_published', 'tutorial__learning_path', 'passing_score']
    search_fields = ['title', 'description']
    inlines = [QuizQuestionInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'tutorial', 'learning_path')
        }),
        ('Configuration', {
            'fields': ('time_limit', 'passing_score', 'max_attempts', 'randomize_questions', 'show_correct_answers')
        }),
        ('Status', {
            'fields': ('is_published',)
        }),
        ('Metadata', {
            'fields': ('created_by',),
            'classes': ('collapse',)
        })
    )

    def average_score_display(self, obj):
        score = obj.average_score
        color = 'green' if score >= 70 else 'orange' if score >= 40 else 'red'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, score
        )
    average_score_display.short_description = 'Average Score'

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class QuizChoiceInline(admin.TabularInline):
    model = QuizChoice
    extra = 2
    fields = ['choice_text', 'is_correct', 'order']


@admin.register(QuizQuestion)
class QuizQuestionAdmin(admin.ModelAdmin):
    list_display = ['question_text_short', 'quiz', 'question_type', 'points', 'order']
    list_filter = ['question_type', 'quiz__tutorial__learning_path']
    search_fields = ['question_text', 'quiz__title']
    inlines = [QuizChoiceInline]
    ordering = ['quiz', 'order']

    def question_text_short(self, obj):
        return obj.question_text[:50] + "..." if len(obj.question_text) > 50 else obj.question_text
    question_text_short.short_description = 'Question'


@admin.register(UserProgress)
class UserProgressAdmin(admin.ModelAdmin):
    list_display = ['user', 'learning_path', 'completion_percentage', 'is_completed', 'last_accessed']
    list_filter = ['is_completed', 'is_started', 'learning_path', 'learning_path__difficulty_level']
    search_fields = ['user__username', 'user__email', 'learning_path__title']
    readonly_fields = ['completion_percentage', 'started_at', 'completed_at', 'created_at', 'updated_at']
    filter_horizontal = ['completed_tutorials']
    ordering = ['-last_accessed']

    fieldsets = (
        ('User & Path', {
            'fields': ('user', 'learning_path', 'current_tutorial')
        }),
        ('Progress', {
            'fields': ('completion_percentage', 'completed_tutorials', 'is_started', 'is_completed')
        }),
        ('Timestamps', {
            'fields': ('started_at', 'completed_at', 'last_accessed'),
            'classes': ('collapse',)
        }),
        ('Performance', {
            'fields': ('total_time_spent', 'average_quiz_score'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(TutorialProgress)
class TutorialProgressAdmin(admin.ModelAdmin):
    list_display = ['user', 'tutorial', 'completion_percentage', 'is_completed', 'quiz_score', 'last_accessed']
    list_filter = ['is_completed', 'is_started', 'tutorial__learning_path', 'tutorial__difficulty_level']
    search_fields = ['user__username', 'user__email', 'tutorial__title']
    readonly_fields = ['completion_percentage', 'started_at', 'completed_at', 'created_at', 'updated_at']
    filter_horizontal = ['completed_steps']
    ordering = ['-last_accessed']


@admin.register(QuizAttempt)
class QuizAttemptAdmin(admin.ModelAdmin):
    list_display = ['user', 'quiz', 'attempt_number', 'percentage', 'is_passed', 'completed_at']
    list_filter = ['is_completed', 'is_passed', 'quiz__tutorial__learning_path']
    search_fields = ['user__username', 'user__email', 'quiz__title']
    readonly_fields = ['score', 'max_score', 'percentage', 'is_passed', 'started_at', 'completed_at', 'created_at', 'updated_at']
    ordering = ['-created_at']

    fieldsets = (
        ('Attempt Details', {
            'fields': ('user', 'quiz', 'attempt_number')
        }),
        ('Results', {
            'fields': ('score', 'max_score', 'percentage', 'is_completed', 'is_passed')
        }),
        ('Timing', {
            'fields': ('started_at', 'completed_at', 'time_taken'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(QuizAnswer)
class QuizAnswerAdmin(admin.ModelAdmin):
    list_display = ['attempt', 'question_short', 'selected_choice_short', 'is_correct', 'answered_at']
    list_filter = ['is_correct', 'question__question_type', 'attempt__quiz__tutorial__learning_path']
    search_fields = ['attempt__user__username', 'question__question_text']
    readonly_fields = ['answered_at']
    ordering = ['-answered_at']

    def question_short(self, obj):
        return obj.question.question_text[:30] + "..." if len(obj.question.question_text) > 30 else obj.question.question_text
    question_short.short_description = 'Question'

    def selected_choice_short(self, obj):
        if obj.selected_choice:
            return obj.selected_choice.choice_text[:30] + "..." if len(obj.selected_choice.choice_text) > 30 else obj.selected_choice.choice_text
        elif obj.text_answer:
            return obj.text_answer[:30] + "..." if len(obj.text_answer) > 30 else obj.text_answer
        elif obj.numerical_answer is not None:
            return str(obj.numerical_answer)
        return "No answer"
    selected_choice_short.short_description = 'Answer'
