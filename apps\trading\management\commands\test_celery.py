"""
Management command to test Celery tasks.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.trading.tasks import (
    process_pending_orders,
    process_advanced_orders,
    update_portfolio_values,
    send_risk_alerts,
    update_trailing_stops,
    cleanup_expired_orders
)


class Command(BaseCommand):
    help = 'Test Celery tasks for trading operations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--task',
            type=str,
            help='Specific task to test (orders, advanced, portfolios, risk, trailing, cleanup)',
        )
        parser.add_argument(
            '--async',
            action='store_true',
            help='Run tasks asynchronously (requires Celery worker)',
        )

    def handle(self, *args, **options):
        """Test Celery tasks."""
        task_name = options.get('task')
        run_async = options.get('async', False)
        
        self.stdout.write(f'Testing Celery tasks at {timezone.now()}')
        
        if run_async:
            self.stdout.write(
                self.style.WARNING('Running tasks asynchronously - make sure Celery worker is running')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('Running tasks synchronously for testing')
            )
        
        try:
            if task_name == 'orders' or not task_name:
                self.test_process_orders(run_async)
            
            if task_name == 'advanced' or not task_name:
                self.test_advanced_orders(run_async)
            
            if task_name == 'portfolios' or not task_name:
                self.test_portfolio_updates(run_async)
            
            if task_name == 'risk' or not task_name:
                self.test_risk_alerts(run_async)
            
            if task_name == 'trailing' or not task_name:
                self.test_trailing_stops(run_async)
            
            if task_name == 'cleanup' or not task_name:
                self.test_cleanup_orders(run_async)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error testing tasks: {str(e)}')
            )
    
    def test_process_orders(self, run_async=False):
        """Test order processing task."""
        self.stdout.write('Testing order processing...')
        
        if run_async:
            result = process_pending_orders.delay()
            self.stdout.write(f'Task queued with ID: {result.id}')
        else:
            result = process_pending_orders()
            self.stdout.write(f'Orders processed: {result}')
    
    def test_advanced_orders(self, run_async=False):
        """Test advanced order processing task."""
        self.stdout.write('Testing advanced order processing...')
        
        if run_async:
            result = process_advanced_orders.delay()
            self.stdout.write(f'Task queued with ID: {result.id}')
        else:
            result = process_advanced_orders()
            self.stdout.write(f'Advanced orders processed: {result}')
    
    def test_portfolio_updates(self, run_async=False):
        """Test portfolio update task."""
        self.stdout.write('Testing portfolio updates...')
        
        if run_async:
            result = update_portfolio_values.delay()
            self.stdout.write(f'Task queued with ID: {result.id}')
        else:
            result = update_portfolio_values()
            self.stdout.write(f'Portfolios updated: {result}')
    
    def test_risk_alerts(self, run_async=False):
        """Test risk alerts task."""
        self.stdout.write('Testing risk alerts...')
        
        if run_async:
            result = send_risk_alerts.delay()
            self.stdout.write(f'Task queued with ID: {result.id}')
        else:
            result = send_risk_alerts()
            self.stdout.write(f'Risk alerts sent: {result}')
    
    def test_trailing_stops(self, run_async=False):
        """Test trailing stops update task."""
        self.stdout.write('Testing trailing stops update...')
        
        if run_async:
            result = update_trailing_stops.delay()
            self.stdout.write(f'Task queued with ID: {result.id}')
        else:
            result = update_trailing_stops()
            self.stdout.write(f'Trailing stops updated: {result}')
    
    def test_cleanup_orders(self, run_async=False):
        """Test order cleanup task."""
        self.stdout.write('Testing order cleanup...')
        
        if run_async:
            result = cleanup_expired_orders.delay()
            self.stdout.write(f'Task queued with ID: {result.id}')
        else:
            result = cleanup_expired_orders()
            self.stdout.write(f'Orders cleaned up: {result}')
