"""
ASGI config for trading_simulator project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "trading_simulator.settings.production")

django_asgi_app = get_asgi_application()

# Import routing after Django setup
from apps.market_data.routing import websocket_urlpatterns as market_data_patterns
from apps.trading.routing import websocket_urlpatterns as trading_patterns

# Combine all WebSocket URL patterns
all_websocket_patterns = market_data_patterns + trading_patterns

application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": AuthMiddlewareStack(
        URLRouter(
            all_websocket_patterns
        )
    ),
})
