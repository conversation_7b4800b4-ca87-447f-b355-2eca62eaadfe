from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
import uuid

User = get_user_model()


class Account(models.Model):
    """
    Virtual trading account model.
    Each user can have multiple accounts with different types and purposes.
    """

    ACCOUNT_TYPE_CHOICES = [
        ('practice', 'Practice Account - Learn trading basics'),
        ('pro', 'Pro Account - Advanced features and higher limits'),
        ('competition', 'Competition Account - For trading contests'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('closed', 'Closed'),
    ]

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='trading_accounts')
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPE_CHOICES, default='practice')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')

    # Account details
    name = models.CharField(max_length=100, help_text="Custom name for the account")
    description = models.TextField(blank=True, help_text="Optional description of account purpose")

    # Balance information
    initial_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('10000.00'),
        validators=[MinValueValidator(Decimal('1000.00')), MaxValueValidator(Decimal('1000000.00'))],
        help_text="Starting balance for this account"
    )

    current_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('10000.00'),
        help_text="Current available balance"
    )

    equity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('10000.00'),
        help_text="Current equity (balance + unrealized P&L)"
    )

    # Performance tracking
    total_deposits = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_withdrawals = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    realized_pnl = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    unrealized_pnl = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Account limits based on type
    max_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('100000.00'),
        help_text="Maximum balance allowed for this account type"
    )

    daily_deposit_limit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('10000.00'),
        help_text="Maximum daily deposit amount"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'trading_accounts'
        verbose_name = 'Trading Account'
        verbose_name_plural = 'Trading Accounts'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.name} ({self.get_account_type_display()})"

    def save(self, *args, **kwargs):
        """Override save to set account limits based on type."""
        if not self.name:
            self.name = f"{self.get_account_type_display()} Account"

        # Set limits based on account type
        if self.account_type == 'practice':
            self.max_balance = Decimal('100000.00')
            self.daily_deposit_limit = Decimal('10000.00')
        elif self.account_type == 'pro':
            self.max_balance = Decimal('500000.00')
            self.daily_deposit_limit = Decimal('50000.00')
        elif self.account_type == 'competition':
            self.max_balance = Decimal('1000000.00')
            self.daily_deposit_limit = Decimal('100000.00')

        super().save(*args, **kwargs)

    @property
    def total_return(self):
        """Calculate total return percentage."""
        if self.initial_balance > 0:
            return ((self.equity - self.initial_balance) / self.initial_balance) * 100
        return Decimal('0.00')

    @property
    def available_balance(self):
        """Get available balance for trading."""
        return self.current_balance

    def can_deposit(self, amount):
        """Check if deposit amount is allowed."""
        if self.status != 'active':
            return False, "Account is not active"

        if amount <= 0:
            return False, "Deposit amount must be positive"

        if amount > self.daily_deposit_limit:
            return False, f"Deposit exceeds daily limit of ${self.daily_deposit_limit:,.2f}"

        if (self.current_balance + amount) > self.max_balance:
            return False, f"Deposit would exceed maximum balance of ${self.max_balance:,.2f}"

        return True, "Deposit allowed"

    def update_balance(self, amount, transaction_type, description=""):
        """Update account balance and create transaction record."""
        if transaction_type in ['deposit', 'credit']:
            self.current_balance += amount
            if transaction_type == 'deposit':
                self.total_deposits += amount
        elif transaction_type in ['withdrawal', 'debit']:
            self.current_balance -= amount
            if transaction_type == 'withdrawal':
                self.total_withdrawals += amount

        self.equity = self.current_balance + self.unrealized_pnl
        self.last_activity = timezone.now()
        self.save()

        # Create transaction record
        Transaction.objects.create(
            account=self,
            transaction_type=transaction_type,
            amount=amount,
            balance_after=self.current_balance,
            description=description
        )


class Transaction(models.Model):
    """
    Transaction model to track all account balance changes.
    """

    TRANSACTION_TYPE_CHOICES = [
        ('deposit', 'Virtual Deposit'),
        ('withdrawal', 'Virtual Withdrawal'),
        ('trade_buy', 'Stock Purchase'),
        ('trade_sell', 'Stock Sale'),
        ('dividend', 'Dividend Payment'),
        ('fee', 'Trading Fee'),
        ('credit', 'Account Credit'),
        ('debit', 'Account Debit'),
        ('transfer_in', 'Transfer In'),
        ('transfer_out', 'Transfer Out'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='transactions')

    # Transaction details
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='completed')

    # Financial information
    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Transaction amount (positive for credits, negative for debits)"
    )

    balance_before = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Account balance before this transaction"
    )

    balance_after = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Account balance after this transaction"
    )

    # Additional information
    description = models.TextField(blank=True, help_text="Transaction description or notes")
    reference_id = models.CharField(max_length=100, blank=True, help_text="External reference ID")

    # Related trading information (for trade transactions)
    symbol = models.CharField(max_length=10, blank=True, help_text="Stock symbol for trade transactions")
    quantity = models.IntegerField(null=True, blank=True, help_text="Number of shares for trade transactions")
    price_per_share = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Price per share for trade transactions"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'trading_transactions'
        verbose_name = 'Transaction'
        verbose_name_plural = 'Transactions'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['account', '-created_at']),
            models.Index(fields=['transaction_type', '-created_at']),
            models.Index(fields=['status', '-created_at']),
        ]

    def __str__(self):
        return f"{self.get_transaction_type_display()} - ${self.amount:,.2f} ({self.account.name})"

    def save(self, *args, **kwargs):
        """Override save to set processed_at for completed transactions."""
        if self.status == 'completed' and not self.processed_at:
            self.processed_at = timezone.now()

        # Set balance_before if not provided
        if self.balance_before is None and self.account:
            self.balance_before = self.account.current_balance

        super().save(*args, **kwargs)

    @property
    def is_credit(self):
        """Check if transaction is a credit (increases balance)."""
        return self.transaction_type in ['deposit', 'trade_sell', 'dividend', 'credit', 'transfer_in']

    @property
    def is_debit(self):
        """Check if transaction is a debit (decreases balance)."""
        return self.transaction_type in ['withdrawal', 'trade_buy', 'fee', 'debit', 'transfer_out']

    @property
    def display_amount(self):
        """Get formatted amount with proper sign."""
        if self.is_credit:
            return f"+${self.amount:,.2f}"
        else:
            return f"-${self.amount:,.2f}"


class Portfolio(models.Model):
    """
    Portfolio model to track user's investment holdings.
    Each account can have one portfolio containing multiple positions.
    """

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    account = models.OneToOneField(Account, on_delete=models.CASCADE, related_name='portfolio')

    # Portfolio metrics
    total_value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Current total value of all positions"
    )

    total_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Total cost basis of all positions"
    )

    unrealized_pnl = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Unrealized profit/loss"
    )

    realized_pnl = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Realized profit/loss from closed positions"
    )

    # Performance tracking
    day_change = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Portfolio value change today"
    )

    day_change_percent = models.DecimalField(
        max_digits=8,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Portfolio percentage change today"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_calculated = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'trading_portfolios'
        verbose_name = 'Portfolio'
        verbose_name_plural = 'Portfolios'

    def __str__(self):
        return f"Portfolio for {self.account.name} ({self.account.user.username})"

    @property
    def total_return(self):
        """Calculate total return percentage."""
        if self.total_cost > 0:
            return ((self.total_value - self.total_cost) / self.total_cost) * 100
        return Decimal('0.00')

    @property
    def cash_value(self):
        """Get available cash (account balance)."""
        return self.account.current_balance

    @property
    def total_portfolio_value(self):
        """Get total portfolio value including cash."""
        return self.total_value + self.cash_value

    @property
    def position_count(self):
        """Get number of open positions."""
        return self.positions.filter(quantity__gt=0).count()

    def calculate_portfolio_value(self):
        """Calculate and update portfolio value based on current positions."""
        positions = self.positions.filter(quantity__gt=0)

        total_value = Decimal('0.00')
        total_cost = Decimal('0.00')

        for position in positions:
            position_value = position.current_value
            position_cost = position.total_cost

            total_value += position_value
            total_cost += position_cost

        self.total_value = total_value
        self.total_cost = total_cost
        self.unrealized_pnl = total_value - total_cost
        self.last_calculated = timezone.now()
        self.save()

        # Update account equity
        self.account.equity = self.account.current_balance + self.total_value
        self.account.unrealized_pnl = self.unrealized_pnl
        self.account.save()

        return self.total_value

    def get_allocation_data(self):
        """Get portfolio allocation data for charts."""
        positions = self.positions.filter(quantity__gt=0)
        allocation_data = []

        for position in positions:
            allocation_data.append({
                'symbol': position.symbol,
                'value': float(position.current_value),
                'percentage': float((position.current_value / self.total_value) * 100) if self.total_value > 0 else 0,
                'quantity': position.quantity,
            })

        # Add cash allocation
        if self.cash_value > 0:
            cash_percentage = float((self.cash_value / self.total_portfolio_value) * 100) if self.total_portfolio_value > 0 else 0
            allocation_data.append({
                'symbol': 'CASH',
                'value': float(self.cash_value),
                'percentage': cash_percentage,
                'quantity': 1,
            })

        return allocation_data


class Position(models.Model):
    """
    Position model to track individual stock holdings within a portfolio.
    """

    POSITION_TYPE_CHOICES = [
        ('long', 'Long Position'),
        ('short', 'Short Position'),
    ]

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='positions')

    # Stock information
    symbol = models.CharField(max_length=10, help_text="Stock symbol (e.g., AAPL, GOOGL)")
    company_name = models.CharField(max_length=200, blank=True, help_text="Company name")

    # Position details
    position_type = models.CharField(max_length=10, choices=POSITION_TYPE_CHOICES, default='long')
    quantity = models.IntegerField(help_text="Number of shares held")

    # Price information
    average_price = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        help_text="Average cost per share"
    )

    current_price = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Current market price per share"
    )

    # Cost basis and value
    total_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Total cost basis (quantity × average_price)"
    )

    # Performance tracking
    unrealized_pnl = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Unrealized profit/loss"
    )

    day_change = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Position value change today"
    )

    day_change_percent = models.DecimalField(
        max_digits=8,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Position percentage change today"
    )

    # Timestamps
    opened_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_price_update = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'trading_positions'
        verbose_name = 'Position'
        verbose_name_plural = 'Positions'
        unique_together = ['portfolio', 'symbol']  # One position per symbol per portfolio
        indexes = [
            models.Index(fields=['portfolio', 'symbol']),
            models.Index(fields=['symbol', '-updated_at']),
        ]

    def __str__(self):
        return f"{self.symbol} - {self.quantity} shares ({self.portfolio.account.user.username})"

    def save(self, *args, **kwargs):
        """Override save to calculate total cost and update portfolio."""
        self.total_cost = Decimal(str(self.quantity)) * self.average_price
        super().save(*args, **kwargs)

        # Update portfolio value after position changes
        if hasattr(self, 'portfolio'):
            self.portfolio.calculate_portfolio_value()

    @property
    def current_value(self):
        """Calculate current market value of the position."""
        return Decimal(str(self.quantity)) * self.current_price

    @property
    def unrealized_pnl_percent(self):
        """Calculate unrealized P&L percentage."""
        if self.total_cost > 0:
            return ((self.current_value - self.total_cost) / self.total_cost) * 100
        return Decimal('0.00')

    @property
    def is_profitable(self):
        """Check if position is currently profitable."""
        return self.unrealized_pnl > 0

    def update_current_price(self, new_price):
        """Update current price and recalculate metrics."""
        old_value = self.current_value
        self.current_price = Decimal(str(new_price))
        new_value = self.current_value

        # Calculate unrealized P&L
        self.unrealized_pnl = new_value - self.total_cost

        # Calculate day change (simplified - would need previous day's closing price)
        self.day_change = new_value - old_value
        if old_value > 0:
            self.day_change_percent = (self.day_change / old_value) * 100

        self.last_price_update = timezone.now()
        self.save()

    def add_shares(self, quantity, price):
        """Add shares to existing position (average down/up)."""
        if quantity <= 0:
            raise ValueError("Quantity must be positive")

        # Calculate new average price
        current_total_cost = self.total_cost
        additional_cost = Decimal(str(quantity)) * Decimal(str(price))
        new_total_cost = current_total_cost + additional_cost
        new_total_quantity = self.quantity + quantity

        self.average_price = new_total_cost / Decimal(str(new_total_quantity))
        self.quantity = new_total_quantity
        self.total_cost = new_total_cost
        self.save()

    def reduce_shares(self, quantity, price):
        """Reduce shares from position (partial or full sale)."""
        if quantity <= 0:
            raise ValueError("Quantity must be positive")
        if quantity > self.quantity:
            raise ValueError("Cannot sell more shares than owned")

        # Calculate realized P&L for sold shares
        cost_per_share = self.average_price
        sale_proceeds = Decimal(str(quantity)) * Decimal(str(price))
        cost_of_sold_shares = Decimal(str(quantity)) * cost_per_share
        realized_pnl = sale_proceeds - cost_of_sold_shares

        # Update position
        self.quantity -= quantity
        self.total_cost = Decimal(str(self.quantity)) * self.average_price

        if self.quantity == 0:
            # Position fully closed
            self.current_price = Decimal('0.0000')
            self.unrealized_pnl = Decimal('0.00')

        self.save()

        # Update portfolio realized P&L
        self.portfolio.realized_pnl += realized_pnl
        self.portfolio.save()

        return realized_pnl


class Order(models.Model):
    """
    Order model for trading operations.
    Supports market, limit, and stop orders with comprehensive tracking.
    """

    ORDER_TYPE_CHOICES = [
        ('market', 'Market Order'),
        ('limit', 'Limit Order'),
        ('stop', 'Stop Order'),
        ('stop_limit', 'Stop Limit Order'),
        ('trailing_stop', 'Trailing Stop Order'),
        ('bracket', 'Bracket Order'),
        ('conditional', 'Conditional Order'),
    ]

    ORDER_SIDE_CHOICES = [
        ('buy', 'Buy'),
        ('sell', 'Sell'),
    ]

    ORDER_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('partially_filled', 'Partially Filled'),
        ('filled', 'Filled'),
        ('cancelled', 'Cancelled'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired'),
    ]

    TIME_IN_FORCE_CHOICES = [
        ('day', 'Day Order'),
        ('gtc', 'Good Till Cancelled'),
        ('ioc', 'Immediate or Cancel'),
        ('fok', 'Fill or Kill'),
    ]

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='orders')

    # Order details
    symbol = models.CharField(max_length=20, help_text="Trading symbol (e.g., AAPL)")
    order_type = models.CharField(max_length=20, choices=ORDER_TYPE_CHOICES)
    side = models.CharField(max_length=10, choices=ORDER_SIDE_CHOICES)
    quantity = models.IntegerField(validators=[MinValueValidator(1)], help_text="Number of shares")

    # Pricing
    price = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Limit price (null for market orders)"
    )

    stop_price = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Stop price for stop orders"
    )

    # Execution details
    filled_quantity = models.IntegerField(default=0, help_text="Number of shares filled")
    average_fill_price = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Average price of filled shares"
    )

    # Order management
    status = models.CharField(max_length=20, choices=ORDER_STATUS_CHOICES, default='pending')
    time_in_force = models.CharField(max_length=10, choices=TIME_IN_FORCE_CHOICES, default='day')

    # Financial details
    estimated_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Estimated total cost including fees"
    )

    commission = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Commission charged for this order"
    )

    total_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Actual total cost after execution"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    filled_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    # Advanced order fields
    trailing_amount = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Trailing amount for trailing stop orders"
    )

    trailing_percent = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Trailing percentage for trailing stop orders"
    )

    parent_order = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='child_orders',
        help_text="Parent order for bracket/OCO orders"
    )

    condition_symbol = models.CharField(
        max_length=20,
        blank=True,
        help_text="Symbol to watch for conditional orders"
    )

    condition_price = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Trigger price for conditional orders"
    )

    condition_operator = models.CharField(
        max_length=10,
        choices=[
            ('gte', 'Greater Than or Equal'),
            ('lte', 'Less Than or Equal'),
            ('eq', 'Equal To'),
        ],
        blank=True,
        help_text="Condition operator for conditional orders"
    )

    highest_price = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Highest price reached (for trailing stops)"
    )

    lowest_price = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Lowest price reached (for trailing stops)"
    )

    # Additional fields
    notes = models.TextField(blank=True, help_text="Order notes or comments")

    class Meta:
        db_table = 'trading_orders'
        verbose_name = 'Order'
        verbose_name_plural = 'Orders'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['account', '-created_at']),
            models.Index(fields=['symbol', 'status']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['side', 'symbol']),
        ]

    def __str__(self):
        return f"{self.get_side_display()} {self.quantity} {self.symbol} @ {self.get_order_type_display()} ({self.get_status_display()})"

    def save(self, *args, **kwargs):
        """Override save to calculate estimated cost and validate order."""
        self.clean()

        # Calculate estimated cost for new orders
        if not self.pk and self.status == 'pending':
            self.calculate_estimated_cost()

        super().save(*args, **kwargs)

    def clean(self):
        """Validate order data."""
        from django.core.exceptions import ValidationError

        # Validate price requirements based on order type
        if self.order_type in ['limit', 'stop_limit'] and not self.price:
            raise ValidationError("Limit orders require a price")

        if self.order_type in ['stop', 'stop_limit'] and not self.stop_price:
            raise ValidationError("Stop orders require a stop price")

        if self.order_type == 'market' and self.price:
            raise ValidationError("Market orders cannot have a limit price")

        # Validate quantity
        if self.quantity <= 0:
            raise ValidationError("Quantity must be positive")

        # Validate filled quantity
        if self.filled_quantity > self.quantity:
            raise ValidationError("Filled quantity cannot exceed order quantity")

    def calculate_estimated_cost(self):
        """Calculate estimated cost including commission."""
        if self.order_type == 'market':
            # For market orders, use current market price
            from apps.market_data.models import Symbol, MarketData
            try:
                symbol_obj = Symbol.objects.get(symbol=self.symbol)
                latest_data = MarketData.objects.filter(symbol=symbol_obj, is_latest=True).first()
                if latest_data:
                    estimated_price = latest_data.price
                else:
                    estimated_price = Decimal('100.00')  # Fallback price
            except Symbol.DoesNotExist:
                estimated_price = Decimal('100.00')  # Fallback price
        else:
            # For limit orders, use the limit price
            estimated_price = self.price or Decimal('100.00')

        # Calculate base cost
        base_cost = Decimal(str(self.quantity)) * estimated_price

        # Calculate commission (flat $1 per trade for simulation)
        self.commission = Decimal('1.00')

        # Total estimated cost
        if self.side == 'buy':
            self.estimated_cost = base_cost + self.commission
        else:
            self.estimated_cost = base_cost - self.commission

    @property
    def remaining_quantity(self):
        """Get remaining quantity to be filled."""
        return self.quantity - self.filled_quantity

    @property
    def is_filled(self):
        """Check if order is completely filled."""
        return self.filled_quantity >= self.quantity

    @property
    def is_partially_filled(self):
        """Check if order is partially filled."""
        return 0 < self.filled_quantity < self.quantity

    @property
    def fill_percentage(self):
        """Get fill percentage."""
        if self.quantity > 0:
            return (self.filled_quantity / self.quantity) * 100
        return 0

    def can_cancel(self):
        """Check if order can be cancelled."""
        return self.status in ['pending', 'partially_filled']

    def can_modify(self):
        """Check if order can be modified."""
        return self.status == 'pending' and self.filled_quantity == 0

    def cancel_order(self, reason="User cancelled"):
        """Cancel the order."""
        if not self.can_cancel():
            raise ValueError(f"Cannot cancel order in {self.status} status")

        self.status = 'cancelled'
        self.notes = f"{self.notes}\nCancelled: {reason}" if self.notes else f"Cancelled: {reason}"
        self.save()

        return True

    def update_trailing_stop(self, current_price):
        """Update trailing stop price based on current market price."""
        if self.order_type != 'trailing_stop':
            return False

        if not self.trailing_amount and not self.trailing_percent:
            return False

        # Initialize highest/lowest price if not set
        if self.side == 'sell':
            if not self.highest_price or current_price > self.highest_price:
                self.highest_price = current_price

                # Calculate new stop price
                if self.trailing_amount:
                    new_stop_price = current_price - self.trailing_amount
                else:  # trailing_percent
                    new_stop_price = current_price * (Decimal('1') - self.trailing_percent / Decimal('100'))

                # Only update if new stop price is higher (for sell orders)
                if not self.stop_price or new_stop_price > self.stop_price:
                    self.stop_price = new_stop_price
                    self.save()
                    return True

        else:  # buy order
            if not self.lowest_price or current_price < self.lowest_price:
                self.lowest_price = current_price

                # Calculate new stop price
                if self.trailing_amount:
                    new_stop_price = current_price + self.trailing_amount
                else:  # trailing_percent
                    new_stop_price = current_price * (Decimal('1') + self.trailing_percent / Decimal('100'))

                # Only update if new stop price is lower (for buy orders)
                if not self.stop_price or new_stop_price < self.stop_price:
                    self.stop_price = new_stop_price
                    self.save()
                    return True

        return False

    def check_condition(self):
        """Check if conditional order condition is met."""
        if self.order_type != 'conditional':
            return True

        if not self.condition_symbol or not self.condition_price or not self.condition_operator:
            return False

        # Get current price of condition symbol
        from apps.market_data.models import Symbol, MarketData
        try:
            symbol_obj = Symbol.objects.get(symbol=self.condition_symbol)
            latest_data = MarketData.objects.filter(symbol=symbol_obj, is_latest=True).first()
            if not latest_data:
                return False

            current_price = latest_data.price

            # Check condition
            if self.condition_operator == 'gte':
                return current_price >= self.condition_price
            elif self.condition_operator == 'lte':
                return current_price <= self.condition_price
            elif self.condition_operator == 'eq':
                return abs(current_price - self.condition_price) < Decimal('0.01')

        except Symbol.DoesNotExist:
            return False

        return False

    def is_bracket_order(self):
        """Check if this is a bracket order."""
        return self.order_type == 'bracket' or self.parent_order is not None

    def get_bracket_orders(self):
        """Get all orders in the bracket (parent and children)."""
        if self.parent_order:
            # This is a child order, get parent and siblings
            parent = self.parent_order
            children = parent.child_orders.all()
            return [parent] + list(children)
        else:
            # This might be a parent order
            children = self.child_orders.all()
            if children.exists():
                return [self] + list(children)

        return [self]

    def cancel_bracket_orders(self):
        """Cancel all orders in the bracket."""
        bracket_orders = self.get_bracket_orders()
        cancelled_count = 0

        for order in bracket_orders:
            if order.can_cancel():
                order.cancel_order("Bracket order cancelled")
                cancelled_count += 1

        return cancelled_count


class Trade(models.Model):
    """
    Trade model to track individual trade executions.
    Each order can result in multiple trades (partial fills).
    """

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='trades')

    # Trade details
    symbol = models.CharField(max_length=20, help_text="Trading symbol")
    side = models.CharField(max_length=10, choices=Order.ORDER_SIDE_CHOICES)
    quantity = models.IntegerField(validators=[MinValueValidator(1)], help_text="Number of shares traded")
    price = models.DecimalField(max_digits=12, decimal_places=4, help_text="Execution price per share")

    # Financial details
    gross_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Gross trade amount (quantity × price)"
    )

    commission = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Commission for this trade"
    )

    net_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Net trade amount after commission"
    )

    # Execution details
    executed_at = models.DateTimeField(auto_now_add=True)
    execution_venue = models.CharField(max_length=50, default='SIMULATOR', help_text="Execution venue")

    # Additional fields
    notes = models.TextField(blank=True, help_text="Trade notes")

    class Meta:
        db_table = 'trading_trades'
        verbose_name = 'Trade'
        verbose_name_plural = 'Trades'
        ordering = ['-executed_at']
        indexes = [
            models.Index(fields=['order', '-executed_at']),
            models.Index(fields=['symbol', '-executed_at']),
            models.Index(fields=['-executed_at']),
        ]

    def __str__(self):
        return f"{self.get_side_display()} {self.quantity} {self.symbol} @ ${self.price}"

    def save(self, *args, **kwargs):
        """Override save to calculate amounts and update order."""
        # Calculate amounts
        self.gross_amount = Decimal(str(self.quantity)) * self.price

        if not self.commission:
            # Calculate proportional commission based on trade size
            if self.order.commission and self.order.quantity > 0:
                commission_per_share = self.order.commission / Decimal(str(self.order.quantity))
                self.commission = commission_per_share * Decimal(str(self.quantity))
            else:
                self.commission = Decimal('0.00')

        # Calculate net amount
        if self.side == 'buy':
            self.net_amount = self.gross_amount + self.commission
        else:
            self.net_amount = self.gross_amount - self.commission

        super().save(*args, **kwargs)

        # Update order after trade is saved
        self.update_order_after_trade()

    def update_order_after_trade(self):
        """Update order status and fill information after trade execution."""
        order = self.order

        # Recalculate filled quantity and average fill price
        trades = order.trades.all()
        total_filled = sum(trade.quantity for trade in trades)

        if total_filled > 0:
            # Calculate weighted average fill price
            total_value = sum(trade.quantity * trade.price for trade in trades)
            average_price = total_value / Decimal(str(total_filled))

            order.filled_quantity = total_filled
            order.average_fill_price = average_price

            # Calculate total cost
            total_commission = sum(trade.commission for trade in trades)
            if order.side == 'buy':
                order.total_cost = total_value + total_commission
            else:
                order.total_cost = total_value - total_commission

            # Update order status
            if total_filled >= order.quantity:
                order.status = 'filled'
                order.filled_at = timezone.now()
            elif total_filled > 0:
                order.status = 'partially_filled'

            order.save()

        # Update portfolio position
        self.update_portfolio_position()

    def update_portfolio_position(self):
        """Update portfolio position based on this trade."""
        from apps.trading.models import Portfolio, Position

        # Get or create portfolio
        portfolio, _ = Portfolio.objects.get_or_create(account=self.order.account)

        # Get or create position
        position, _ = Position.objects.get_or_create(
            portfolio=portfolio,
            symbol=self.symbol,
            defaults={
                'company_name': '',  # Will be updated later
                'quantity': 0,
                'average_price': Decimal('0.00'),
                'current_price': self.price,
                'total_cost': Decimal('0.00'),
            }
        )

        # Update position based on trade
        if self.side == 'buy':
            # Add shares to position
            if position.quantity > 0:
                # Calculate new average price
                current_value = position.quantity * position.average_price
                additional_value = self.quantity * self.price
                new_total_quantity = position.quantity + self.quantity
                new_average_price = (current_value + additional_value) / Decimal(str(new_total_quantity))

                position.quantity = new_total_quantity
                position.average_price = new_average_price
            else:
                # First purchase
                position.quantity = self.quantity
                position.average_price = self.price
        else:
            # Sell shares from position
            if position.quantity >= self.quantity:
                position.quantity -= self.quantity
                # Average price remains the same for sales
            else:
                # This shouldn't happen with proper validation
                raise ValueError(f"Cannot sell {self.quantity} shares, only {position.quantity} available")

        # Update current price and recalculate
        position.current_price = self.price
        position.save()

        # Update account balance
        self.update_account_balance()

    def update_account_balance(self):
        """Update account balance based on this trade."""
        account = self.order.account

        if self.side == 'buy':
            # Deduct cost from balance
            account.current_balance -= self.net_amount
        else:
            # Add proceeds to balance
            account.current_balance += self.net_amount

        account.save()


class RiskManagement(models.Model):
    """
    Risk management settings and calculations for trading accounts.
    """

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    account = models.OneToOneField(Account, on_delete=models.CASCADE, related_name='risk_management')

    # Position sizing rules
    max_position_size_percent = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('10.00'),
        help_text="Maximum position size as percentage of portfolio"
    )

    max_position_size_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum position size in dollar amount"
    )

    # Risk limits
    max_daily_loss_percent = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('5.00'),
        help_text="Maximum daily loss as percentage of portfolio"
    )

    max_daily_loss_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum daily loss in dollar amount"
    )

    max_drawdown_percent = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('20.00'),
        help_text="Maximum drawdown as percentage of portfolio"
    )

    # Risk/Reward settings
    min_risk_reward_ratio = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('1.50'),
        help_text="Minimum risk/reward ratio for trades"
    )

    # Leverage settings
    max_leverage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('1.00'),
        help_text="Maximum leverage allowed"
    )

    # Margin requirements
    margin_requirement_percent = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('50.00'),
        help_text="Margin requirement as percentage"
    )

    # Alert settings
    enable_risk_alerts = models.BooleanField(default=True)
    enable_drawdown_alerts = models.BooleanField(default=True)
    enable_position_size_alerts = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'trading_risk_management'
        verbose_name = 'Risk Management'
        verbose_name_plural = 'Risk Management Settings'

    def __str__(self):
        return f"Risk Management - {self.account.name}"

    def calculate_position_size(self, symbol_price, risk_amount):
        """Calculate optimal position size based on risk parameters."""
        if not symbol_price or not risk_amount:
            return 0

        # Calculate based on risk amount
        max_shares_by_risk = int(risk_amount / symbol_price)

        # Calculate based on position size limits
        portfolio_value = self.account.equity
        if portfolio_value > 0:
            max_position_value = portfolio_value * (self.max_position_size_percent / Decimal('100'))
            if self.max_position_size_amount:
                max_position_value = min(max_position_value, self.max_position_size_amount)

            max_shares_by_position = int(max_position_value / symbol_price)

            return min(max_shares_by_risk, max_shares_by_position)

        return max_shares_by_risk

    def calculate_risk_reward_ratio(self, entry_price, stop_loss, take_profit):
        """Calculate risk/reward ratio for a trade."""
        if not all([entry_price, stop_loss, take_profit]):
            return None

        risk = abs(entry_price - stop_loss)
        reward = abs(take_profit - entry_price)

        if risk > 0:
            return reward / risk

        return None

    def check_daily_loss_limit(self):
        """Check if daily loss limit has been reached."""
        from django.utils import timezone
        from datetime import datetime

        today = timezone.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_start = timezone.make_aware(today_start)

        # Calculate today's P&L
        today_trades = Trade.objects.filter(
            order__account=self.account,
            executed_at__gte=today_start
        )

        daily_pnl = Decimal('0.00')
        for trade in today_trades:
            if trade.side == 'sell':
                daily_pnl += trade.net_amount
            else:
                daily_pnl -= trade.net_amount

        # Check against limits
        portfolio_value = self.account.equity
        max_loss_by_percent = portfolio_value * (self.max_daily_loss_percent / Decimal('100'))

        if self.max_daily_loss_amount:
            max_loss = min(max_loss_by_percent, self.max_daily_loss_amount)
        else:
            max_loss = max_loss_by_percent

        return daily_pnl <= -max_loss

    def calculate_current_drawdown(self):
        """Calculate current drawdown from peak equity."""
        # Get historical equity values
        equity_history = PortfolioHistory.objects.filter(
            portfolio__account=self.account
        ).order_by('date')

        if not equity_history.exists():
            return Decimal('0.00')

        peak_equity = max(h.total_value for h in equity_history)
        current_equity = self.account.equity

        if peak_equity > 0:
            drawdown = ((peak_equity - current_equity) / peak_equity) * Decimal('100')
            return max(drawdown, Decimal('0.00'))

        return Decimal('0.00')

    def check_drawdown_limit(self):
        """Check if maximum drawdown limit has been reached."""
        current_drawdown = self.calculate_current_drawdown()
        return current_drawdown >= self.max_drawdown_percent


class PortfolioHistory(models.Model):
    """
    Model to track portfolio value over time for performance analysis.
    """

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='history')

    # Snapshot data
    date = models.DateField(help_text="Date of this portfolio snapshot")
    total_value = models.DecimalField(max_digits=15, decimal_places=2)
    cash_value = models.DecimalField(max_digits=15, decimal_places=2)
    positions_value = models.DecimalField(max_digits=15, decimal_places=2)

    # Performance metrics
    day_change = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    day_change_percent = models.DecimalField(max_digits=8, decimal_places=4, default=Decimal('0.0000'))
    total_return = models.DecimalField(max_digits=8, decimal_places=4, default=Decimal('0.0000'))

    # Position count
    position_count = models.IntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'trading_portfolio_history'
        verbose_name = 'Portfolio History'
        verbose_name_plural = 'Portfolio History'
        unique_together = ['portfolio', 'date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['portfolio', '-date']),
            models.Index(fields=['date']),
        ]

    def __str__(self):
        return f"Portfolio History - {self.portfolio.account.name} ({self.date})"

    @classmethod
    def create_snapshot(cls, portfolio):
        """Create a daily snapshot of portfolio performance."""
        today = timezone.now().date()

        # Check if snapshot already exists for today
        snapshot, created = cls.objects.get_or_create(
            portfolio=portfolio,
            date=today,
            defaults={
                'total_value': portfolio.total_portfolio_value,
                'cash_value': portfolio.cash_value,
                'positions_value': portfolio.total_value,
                'total_return': portfolio.total_return,
                'position_count': portfolio.position_count,
            }
        )

        if not created:
            # Update existing snapshot
            snapshot.total_value = portfolio.total_portfolio_value
            snapshot.cash_value = portfolio.cash_value
            snapshot.positions_value = portfolio.total_value
            snapshot.total_return = portfolio.total_return
            snapshot.position_count = portfolio.position_count
            snapshot.save()

        # Calculate day change if previous day exists
        previous_snapshot = cls.objects.filter(
            portfolio=portfolio,
            date__lt=today
        ).first()

        if previous_snapshot:
            snapshot.day_change = snapshot.total_value - previous_snapshot.total_value
            if previous_snapshot.total_value > 0:
                snapshot.day_change_percent = (snapshot.day_change / previous_snapshot.total_value) * 100
            snapshot.save()

        return snapshot

    @classmethod
    def get_performance_data(cls, portfolio, days=30):
        """Get portfolio performance data for charting."""
        end_date = timezone.now().date()
        start_date = end_date - timezone.timedelta(days=days)

        history = cls.objects.filter(
            portfolio=portfolio,
            date__gte=start_date,
            date__lte=end_date
        ).order_by('date')

        return [{
            'date': h.date.isoformat(),
            'total_value': float(h.total_value),
            'cash_value': float(h.cash_value),
            'positions_value': float(h.positions_value),
            'day_change': float(h.day_change),
            'day_change_percent': float(h.day_change_percent),
            'total_return': float(h.total_return),
        } for h in history]
