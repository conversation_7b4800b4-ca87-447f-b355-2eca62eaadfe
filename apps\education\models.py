"""
Educational content models for the trading simulator.
"""

import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone

User = get_user_model()


class DifficultyLevel(models.TextChoices):
    """Difficulty levels for educational content."""
    BEGINNER = 'beginner', 'Beginner'
    INTERMEDIATE = 'intermediate', 'Intermediate'
    ADVANCED = 'advanced', 'Advanced'
    EXPERT = 'expert', 'Expert'


class ContentType(models.TextChoices):
    """Types of educational content."""
    TUTORIAL = 'tutorial', 'Tutorial'
    LESSON = 'lesson', 'Lesson'
    QUIZ = 'quiz', 'Quiz'
    SIMULATION = 'simulation', 'Simulation'
    GUIDE = 'guide', 'Guide'
    VIDEO = 'video', 'Video'


class Category(models.Model):
    """Educational content categories."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="FontAwesome icon class")
    color = models.CharField(max_length=7, default="#007bff", help_text="Hex color code")
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['order', 'name']

    def __str__(self):
        return self.name


class LearningPath(models.Model):
    """Learning paths that group related tutorials and lessons."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    description = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='learning_paths')
    difficulty_level = models.CharField(max_length=20, choices=DifficultyLevel.choices)
    estimated_duration = models.PositiveIntegerField(help_text="Estimated duration in minutes")
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False, related_name='unlocks')

    # Content
    thumbnail = models.URLField(blank=True)
    banner_image = models.URLField(blank=True)
    tags = models.CharField(max_length=500, blank=True, help_text="Comma-separated tags")

    # Status
    is_published = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_paths')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'title']

    def __str__(self):
        return self.title

    @property
    def total_lessons(self):
        """Get total number of lessons in this path."""
        return self.tutorials.filter(is_published=True).count()

    @property
    def completion_rate(self):
        """Get average completion rate for this path."""
        from django.db.models import Avg
        return self.user_progress.aggregate(
            avg_progress=Avg('completion_percentage')
        )['avg_progress'] or 0


class Tutorial(models.Model):
    """Individual tutorials and lessons."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    description = models.TextField()
    learning_path = models.ForeignKey(LearningPath, on_delete=models.CASCADE, related_name='tutorials')
    content_type = models.CharField(max_length=20, choices=ContentType.choices, default=ContentType.TUTORIAL)
    difficulty_level = models.CharField(max_length=20, choices=DifficultyLevel.choices)

    # Content
    content = models.TextField(help_text="Main tutorial content in Markdown")
    summary = models.TextField(blank=True, help_text="Brief summary of the tutorial")
    learning_objectives = models.TextField(blank=True, help_text="What users will learn")

    # Media
    thumbnail = models.URLField(blank=True)
    video_url = models.URLField(blank=True)

    # Structure
    order = models.PositiveIntegerField(default=0)
    estimated_duration = models.PositiveIntegerField(help_text="Estimated duration in minutes")

    # Requirements
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False, related_name='unlocks')
    required_score = models.PositiveIntegerField(default=0, help_text="Minimum score to pass (for quizzes)")

    # Status
    is_published = models.BooleanField(default=False)
    is_interactive = models.BooleanField(default=False)

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_tutorials')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['learning_path', 'order', 'title']
        unique_together = ['learning_path', 'order']

    def __str__(self):
        return f"{self.learning_path.title} - {self.title}"

    @property
    def completion_rate(self):
        """Get completion rate for this tutorial."""
        total_users = self.user_progress.count()
        if total_users == 0:
            return 0
        completed_users = self.user_progress.filter(is_completed=True).count()
        return (completed_users / total_users) * 100


class TutorialStep(models.Model):
    """Individual steps within a tutorial."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tutorial = models.ForeignKey(Tutorial, on_delete=models.CASCADE, related_name='steps')
    title = models.CharField(max_length=200)
    content = models.TextField(help_text="Step content in Markdown")
    step_type = models.CharField(
        max_length=20,
        choices=[
            ('content', 'Content'),
            ('action', 'Action Required'),
            ('quiz', 'Quiz Question'),
            ('simulation', 'Trading Simulation'),
            ('checkpoint', 'Progress Checkpoint'),
        ],
        default='content'
    )

    # Interactive elements
    action_required = models.TextField(blank=True, help_text="Description of required action")
    simulation_config = models.JSONField(blank=True, null=True, help_text="Configuration for trading simulation")

    # Structure
    order = models.PositiveIntegerField(default=0)
    is_optional = models.BooleanField(default=False)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['tutorial', 'order']
        unique_together = ['tutorial', 'order']

    def __str__(self):
        return f"{self.tutorial.title} - Step {self.order}: {self.title}"


class Quiz(models.Model):
    """Quizzes and assessments."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    tutorial = models.ForeignKey(Tutorial, on_delete=models.CASCADE, related_name='quizzes', null=True, blank=True)
    learning_path = models.ForeignKey(LearningPath, on_delete=models.CASCADE, related_name='quizzes', null=True, blank=True)

    # Configuration
    time_limit = models.PositiveIntegerField(null=True, blank=True, help_text="Time limit in minutes")
    passing_score = models.PositiveIntegerField(default=70, validators=[MinValueValidator(0), MaxValueValidator(100)])
    max_attempts = models.PositiveIntegerField(default=3)
    randomize_questions = models.BooleanField(default=True)
    show_correct_answers = models.BooleanField(default=True)

    # Status
    is_published = models.BooleanField(default=False)

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_quizzes')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Quizzes"
        ordering = ['title']

    def __str__(self):
        return self.title

    @property
    def total_questions(self):
        """Get total number of questions in this quiz."""
        return self.questions.count()

    @property
    def average_score(self):
        """Get average score for this quiz."""
        from django.db.models import Avg
        return self.attempts.aggregate(avg_score=Avg('score'))['avg_score'] or 0


class QuizQuestion(models.Model):
    """Individual quiz questions."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='questions')
    question_text = models.TextField()
    question_type = models.CharField(
        max_length=20,
        choices=[
            ('multiple_choice', 'Multiple Choice'),
            ('true_false', 'True/False'),
            ('short_answer', 'Short Answer'),
            ('numerical', 'Numerical'),
        ],
        default='multiple_choice'
    )

    # Configuration
    points = models.PositiveIntegerField(default=1)
    explanation = models.TextField(blank=True, help_text="Explanation of the correct answer")
    order = models.PositiveIntegerField(default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['quiz', 'order']
        unique_together = ['quiz', 'order']

    def __str__(self):
        return f"{self.quiz.title} - Q{self.order}: {self.question_text[:50]}..."


class QuizChoice(models.Model):
    """Answer choices for quiz questions."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    question = models.ForeignKey(QuizQuestion, on_delete=models.CASCADE, related_name='choices')
    choice_text = models.TextField()
    is_correct = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['question', 'order']

    def __str__(self):
        return f"{self.question.question_text[:30]}... - {self.choice_text[:30]}..."


class UserProgress(models.Model):
    """Track user progress through learning paths and tutorials."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='learning_progress')
    learning_path = models.ForeignKey(LearningPath, on_delete=models.CASCADE, related_name='user_progress')

    # Progress tracking
    current_tutorial = models.ForeignKey(Tutorial, on_delete=models.SET_NULL, null=True, blank=True)
    completed_tutorials = models.ManyToManyField(Tutorial, blank=True, related_name='completed_by_users')
    completion_percentage = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # Status
    is_started = models.BooleanField(default=False)
    is_completed = models.BooleanField(default=False)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    last_accessed = models.DateTimeField(auto_now=True)

    # Performance
    total_time_spent = models.PositiveIntegerField(default=0, help_text="Total time spent in minutes")
    average_quiz_score = models.FloatField(default=0.0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'learning_path']
        ordering = ['-last_accessed']

    def __str__(self):
        return f"{self.user.username} - {self.learning_path.title} ({self.completion_percentage}%)"

    def start_learning(self):
        """Mark learning path as started."""
        if not self.is_started:
            self.is_started = True
            self.started_at = timezone.now()
            self.save()

    def complete_tutorial(self, tutorial):
        """Mark a tutorial as completed and update progress."""
        if tutorial not in self.completed_tutorials.all():
            self.completed_tutorials.add(tutorial)
            self.update_progress()

    def update_progress(self):
        """Update completion percentage based on completed tutorials."""
        total_tutorials = self.learning_path.tutorials.filter(is_published=True).count()
        if total_tutorials > 0:
            completed_count = self.completed_tutorials.filter(is_published=True).count()
            self.completion_percentage = int((completed_count / total_tutorials) * 100)

            if self.completion_percentage >= 100 and not self.is_completed:
                self.is_completed = True
                self.completed_at = timezone.now()

            self.save()


class TutorialProgress(models.Model):
    """Track user progress through individual tutorials."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tutorial_progress')
    tutorial = models.ForeignKey(Tutorial, on_delete=models.CASCADE, related_name='user_progress')

    # Progress tracking
    current_step = models.ForeignKey(TutorialStep, on_delete=models.SET_NULL, null=True, blank=True)
    completed_steps = models.ManyToManyField(TutorialStep, blank=True, related_name='completed_by_users')
    completion_percentage = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # Status
    is_started = models.BooleanField(default=False)
    is_completed = models.BooleanField(default=False)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    last_accessed = models.DateTimeField(auto_now=True)

    # Performance
    time_spent = models.PositiveIntegerField(default=0, help_text="Time spent in minutes")
    quiz_score = models.FloatField(null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'tutorial']
        ordering = ['-last_accessed']

    def __str__(self):
        return f"{self.user.username} - {self.tutorial.title} ({self.completion_percentage}%)"

    def start_tutorial(self):
        """Mark tutorial as started."""
        if not self.is_started:
            self.is_started = True
            self.started_at = timezone.now()
            self.save()

    def complete_step(self, step):
        """Mark a step as completed and update progress."""
        if step not in self.completed_steps.all():
            self.completed_steps.add(step)
            self.update_progress()

    def update_progress(self):
        """Update completion percentage based on completed steps."""
        total_steps = self.tutorial.steps.count()
        if total_steps > 0:
            completed_count = self.completed_steps.count()
            self.completion_percentage = int((completed_count / total_steps) * 100)

            if self.completion_percentage >= 100 and not self.is_completed:
                self.is_completed = True
                self.completed_at = timezone.now()

            self.save()


class QuizAttempt(models.Model):
    """Track user quiz attempts and scores."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='quiz_attempts')
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='attempts')

    # Attempt details
    attempt_number = models.PositiveIntegerField(default=1)
    score = models.FloatField(default=0.0)
    max_score = models.FloatField(default=0.0)
    percentage = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # Status
    is_completed = models.BooleanField(default=False)
    is_passed = models.BooleanField(default=False)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    time_taken = models.PositiveIntegerField(null=True, blank=True, help_text="Time taken in seconds")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'quiz', 'attempt_number']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.quiz.title} (Attempt {self.attempt_number}): {self.percentage}%"

    def calculate_score(self):
        """Calculate the final score based on answers."""
        total_points = 0
        earned_points = 0

        for answer in self.answers.all():
            total_points += answer.question.points
            if answer.is_correct:
                earned_points += answer.question.points

        self.max_score = total_points
        self.score = earned_points
        self.percentage = int((earned_points / total_points * 100)) if total_points > 0 else 0
        self.is_passed = self.percentage >= self.quiz.passing_score
        self.save()


class QuizAnswer(models.Model):
    """User answers to quiz questions."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    attempt = models.ForeignKey(QuizAttempt, on_delete=models.CASCADE, related_name='answers')
    question = models.ForeignKey(QuizQuestion, on_delete=models.CASCADE)
    selected_choice = models.ForeignKey(QuizChoice, on_delete=models.CASCADE, null=True, blank=True)
    text_answer = models.TextField(blank=True)
    numerical_answer = models.FloatField(null=True, blank=True)
    is_correct = models.BooleanField(default=False)

    # Metadata
    answered_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['attempt', 'question']

    def __str__(self):
        return f"{self.attempt.user.username} - {self.question.question_text[:30]}..."
