{% extends 'base.html' %}
{% load static %}

{% block title %}Learning Paths - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
.filter-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.learning-path-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    overflow: hidden;
}

.learning-path-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.card-img-overlay-custom {
    position: absolute;
    top: 0;
    right: 0;
    padding: 15px;
}

.difficulty-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
}

.difficulty-beginner { background-color: #28a745; }
.difficulty-intermediate { background-color: #ffc107; color: #212529; }
.difficulty-advanced { background-color: #fd7e14; }
.difficulty-expert { background-color: #dc3545; }

.category-filter {
    border: 2px solid transparent;
    border-radius: 10px;
    padding: 10px 15px;
    margin: 5px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.category-filter:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.category-filter.active {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.search-box {
    border-radius: 25px;
    border: 2px solid #e9ecef;
    padding: 10px 20px;
    transition: border-color 0.3s ease;
}

.search-box:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold mb-3">
                <i class="fas fa-graduation-cap me-3"></i>Learning Paths
            </h1>
            <p class="lead">Choose your learning journey and master trading skills step by step.</p>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="filter-section">
        <form method="get" class="row align-items-end">
            <!-- Search -->
            <div class="col-md-4 mb-3">
                <label for="search" class="form-label">Search Learning Paths</label>
                <input type="text" class="form-control search-box" id="search" name="search" 
                       value="{{ search_query }}" placeholder="Search by title, description, or tags...">
            </div>
            
            <!-- Difficulty Filter -->
            <div class="col-md-3 mb-3">
                <label for="difficulty" class="form-label">Difficulty Level</label>
                <select class="form-select" id="difficulty" name="difficulty">
                    <option value="">All Levels</option>
                    <option value="beginner" {% if current_difficulty == 'beginner' %}selected{% endif %}>Beginner</option>
                    <option value="intermediate" {% if current_difficulty == 'intermediate' %}selected{% endif %}>Intermediate</option>
                    <option value="advanced" {% if current_difficulty == 'advanced' %}selected{% endif %}>Advanced</option>
                    <option value="expert" {% if current_difficulty == 'expert' %}selected{% endif %}>Expert</option>
                </select>
            </div>
            
            <!-- Action Buttons -->
            <div class="col-md-5 mb-3">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Filter
                    </button>
                    <a href="{% url 'education:learning_paths' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Clear
                    </a>
                </div>
            </div>
        </form>
        
        <!-- Category Filters -->
        <div class="mt-3">
            <h6 class="mb-3">Filter by Category:</h6>
            <div class="d-flex flex-wrap">
                <a href="{% url 'education:learning_paths' %}" 
                   class="category-filter {% if not current_category %}active{% endif %}">
                    <i class="fas fa-th-large me-2"></i>All Categories
                </a>
                {% for category in categories %}
                <a href="{% url 'education:learning_paths' %}?category={{ category.id }}" 
                   class="category-filter {% if current_category == category.id|stringformat:'s' %}active{% endif %}">
                    <i class="fas {{ category.icon|default:'fa-book' }} me-2" style="color: {{ category.color }};"></i>
                    {{ category.name }}
                </a>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <p class="text-muted">
                {% if search_query or current_category or current_difficulty %}
                    Showing {{ page_obj.paginator.count }} learning path{{ page_obj.paginator.count|pluralize }}
                    {% if search_query %}matching "{{ search_query }}"{% endif %}
                    {% if current_category_obj %}in {{ current_category_obj.name }}{% endif %}
                    {% if current_difficulty %}at {{ current_difficulty }} level{% endif %}
                {% else %}
                    Showing all {{ page_obj.paginator.count }} learning path{{ page_obj.paginator.count|pluralize }}
                {% endif %}
            </p>
        </div>
    </div>

    <!-- Learning Paths Grid -->
    <div class="row">
        {% for path in page_obj %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card learning-path-card">
                <!-- Card Image/Icon -->
                {% if path.thumbnail %}
                <img src="{{ path.thumbnail }}" class="card-img-top" alt="{{ path.title }}" style="height: 200px; object-fit: cover;">
                {% else %}
                <div class="card-img-top d-flex align-items-center justify-content-center" 
                     style="height: 200px; background: linear-gradient(135deg, {{ path.category.color }}22 0%, {{ path.category.color }}44 100%);">
                    <i class="fas {{ path.category.icon|default:'fa-book' }}" style="font-size: 4rem; color: {{ path.category.color }};"></i>
                </div>
                {% endif %}
                
                <!-- Difficulty Badge Overlay -->
                <div class="card-img-overlay-custom">
                    <span class="badge difficulty-{{ path.difficulty_level }} difficulty-badge">
                        {{ path.get_difficulty_level_display }}
                    </span>
                </div>
                
                <!-- Card Body -->
                <div class="card-body d-flex flex-column">
                    <!-- Category -->
                    <div class="mb-2">
                        <span class="badge bg-secondary">
                            <i class="fas fa-tag me-1"></i>{{ path.category.name }}
                        </span>
                    </div>
                    
                    <!-- Title and Description -->
                    <h5 class="card-title">{{ path.title }}</h5>
                    <p class="card-text flex-grow-1">{{ path.description|truncatewords:20 }}</p>
                    
                    <!-- Stats -->
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <small class="text-muted d-block">Duration</small>
                            <strong>{{ path.estimated_duration }} min</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">Tutorials</small>
                            <strong>{{ path.total_lessons }}</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">Level</small>
                            <strong>{{ path.get_difficulty_level_display }}</strong>
                        </div>
                    </div>
                    
                    <!-- Action Button -->
                    <div class="mt-auto">
                        <a href="{% url 'education:learning_path_detail' path.id %}" 
                           class="btn btn-primary w-100">
                            <i class="fas fa-play me-2"></i>Start Learning
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <!-- No Results -->
        <div class="col-12">
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h4>No Learning Paths Found</h4>
                <p class="mb-4">
                    {% if search_query or current_category or current_difficulty %}
                        No learning paths match your current filters. Try adjusting your search criteria.
                    {% else %}
                        No learning paths are available at the moment. Check back soon!
                    {% endif %}
                </p>
                {% if search_query or current_category or current_difficulty %}
                <a href="{% url 'education:learning_paths' %}" class="btn btn-primary">
                    <i class="fas fa-times me-2"></i>Clear Filters
                </a>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="Learning paths pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_difficulty %}&difficulty={{ current_difficulty }}{% endif %}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_difficulty %}&difficulty={{ current_difficulty }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_difficulty %}&difficulty={{ current_difficulty }}{% endif %}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on category filter click
    const categoryFilters = document.querySelectorAll('.category-filter');
    categoryFilters.forEach(filter => {
        filter.addEventListener('click', function(e) {
            if (this.href.includes('category=')) {
                e.preventDefault();
                const url = new URL(this.href);
                const categoryId = url.searchParams.get('category');
                
                // Update the form and submit
                const form = document.querySelector('form');
                let categoryInput = form.querySelector('input[name="category"]');
                if (!categoryInput) {
                    categoryInput = document.createElement('input');
                    categoryInput.type = 'hidden';
                    categoryInput.name = 'category';
                    form.appendChild(categoryInput);
                }
                categoryInput.value = categoryId || '';
                form.submit();
            }
        });
    });
    
    // Search on Enter key
    const searchInput = document.getElementById('search');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            this.form.submit();
        }
    });
});
</script>
{% endblock %}
