"""
Management command to create sample educational content.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.education.models import (
    Category, LearningPath, Tutorial, TutorialStep, Quiz, QuizQuestion, QuizChoice
)

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample educational content for testing'

    def handle(self, *args, **options):
        """Create sample educational content."""
        self.stdout.write('Creating sample educational content...')
        
        # Get or create admin user
        admin_user = User.objects.filter(is_staff=True).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                is_staff=True,
                is_superuser=True
            )
        
        # Create categories
        categories = [
            {
                'name': 'Trading Basics',
                'description': 'Fundamental concepts every trader should know',
                'icon': 'fa-chart-line',
                'color': '#28a745',
                'order': 1
            },
            {
                'name': 'Technical Analysis',
                'description': 'Chart patterns, indicators, and market analysis',
                'icon': 'fa-chart-bar',
                'color': '#007bff',
                'order': 2
            },
            {
                'name': 'Risk Management',
                'description': 'Protecting your capital and managing risk',
                'icon': 'fa-shield-alt',
                'color': '#dc3545',
                'order': 3
            },
            {
                'name': 'Trading Psychology',
                'description': 'Mental aspects of successful trading',
                'icon': 'fa-brain',
                'color': '#6f42c1',
                'order': 4
            }
        ]
        
        created_categories = {}
        for cat_data in categories:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            created_categories[cat_data['name']] = category
            if created:
                self.stdout.write(f'Created category: {category.name}')
        
        # Create learning paths
        learning_paths_data = [
            {
                'title': 'Introduction to Trading',
                'description': 'Learn the fundamentals of trading, market basics, and essential terminology.',
                'category': 'Trading Basics',
                'difficulty_level': 'beginner',
                'estimated_duration': 120,
                'is_published': True,
                'is_featured': True,
                'order': 1,
                'tags': 'basics, fundamentals, beginner'
            },
            {
                'title': 'Chart Reading Mastery',
                'description': 'Master the art of reading charts and identifying key patterns.',
                'category': 'Technical Analysis',
                'difficulty_level': 'intermediate',
                'estimated_duration': 180,
                'is_published': True,
                'is_featured': True,
                'order': 2,
                'tags': 'charts, patterns, technical analysis'
            },
            {
                'title': 'Risk Management Essentials',
                'description': 'Learn how to protect your capital and manage trading risks effectively.',
                'category': 'Risk Management',
                'difficulty_level': 'intermediate',
                'estimated_duration': 150,
                'is_published': True,
                'is_featured': True,
                'order': 3,
                'tags': 'risk, management, capital protection'
            }
        ]
        
        created_paths = {}
        for path_data in learning_paths_data:
            category_name = path_data.pop('category')
            path_data['category'] = created_categories[category_name]
            path_data['created_by'] = admin_user
            
            path, created = LearningPath.objects.get_or_create(
                title=path_data['title'],
                defaults=path_data
            )
            created_paths[path_data['title']] = path
            if created:
                self.stdout.write(f'Created learning path: {path.title}')
        
        # Create tutorials for "Introduction to Trading"
        intro_path = created_paths['Introduction to Trading']
        tutorials_data = [
            {
                'title': 'What is Trading?',
                'description': 'Understanding the basics of financial markets and trading.',
                'content': '''# What is Trading?

Trading is the buying and selling of financial instruments such as stocks, bonds, currencies, or commodities with the goal of making a profit. 

## Key Concepts:

### 1. Financial Markets
Financial markets are platforms where buyers and sellers come together to trade financial instruments. The main types include:
- **Stock Markets**: Where company shares are traded
- **Forex Markets**: Where currencies are exchanged
- **Commodity Markets**: Where raw materials are traded
- **Bond Markets**: Where debt securities are traded

### 2. Types of Trading
- **Day Trading**: Buying and selling within the same day
- **Swing Trading**: Holding positions for days to weeks
- **Position Trading**: Long-term holding (months to years)
- **Scalping**: Very short-term trades (minutes to hours)

### 3. Market Participants
- **Retail Traders**: Individual investors
- **Institutional Traders**: Banks, hedge funds, pension funds
- **Market Makers**: Provide liquidity to markets
- **Brokers**: Facilitate trades between buyers and sellers

## Getting Started
Before you start trading, it's important to:
1. Understand the risks involved
2. Learn about different markets
3. Develop a trading strategy
4. Practice with a demo account
5. Start with small amounts''',
                'order': 1,
                'estimated_duration': 30,
                'is_published': True
            },
            {
                'title': 'Market Orders and Order Types',
                'description': 'Learn about different types of orders and how to use them.',
                'content': '''# Market Orders and Order Types

Understanding different order types is crucial for effective trading. Each order type serves a specific purpose and can help you execute your trading strategy more effectively.

## Basic Order Types

### 1. Market Order
- Executes immediately at the current market price
- Guarantees execution but not price
- Best for liquid markets
- Use when you want immediate execution

### 2. Limit Order
- Executes only at a specified price or better
- Guarantees price but not execution
- Can be used to buy below current price or sell above
- Good for entering positions at specific levels

### 3. Stop Order (Stop-Loss)
- Becomes a market order when price reaches stop level
- Used to limit losses or protect profits
- Essential for risk management
- Should be set before entering any trade

### 4. Stop-Limit Order
- Combines features of stop and limit orders
- Becomes a limit order when stop price is reached
- Provides more control but may not execute

## Advanced Order Types

### 1. Trailing Stop
- Stop price adjusts with favorable price movement
- Helps lock in profits while allowing for upside
- Automatically adjusts stop level

### 2. Bracket Orders
- Combines entry, profit target, and stop-loss
- Automatically manages the entire trade
- Reduces emotional decision-making

## Best Practices
1. Always use stop-losses
2. Set realistic profit targets
3. Understand order execution in different market conditions
4. Practice with different order types in demo accounts''',
                'order': 2,
                'estimated_duration': 25,
                'is_published': True
            }
        ]
        
        created_tutorials = []
        for tutorial_data in tutorials_data:
            tutorial_data['learning_path'] = intro_path
            tutorial_data['content_type'] = 'tutorial'
            tutorial_data['difficulty_level'] = 'beginner'
            tutorial_data['created_by'] = admin_user
            
            tutorial, created = Tutorial.objects.get_or_create(
                title=tutorial_data['title'],
                learning_path=intro_path,
                defaults=tutorial_data
            )
            created_tutorials.append(tutorial)
            if created:
                self.stdout.write(f'Created tutorial: {tutorial.title}')
        
        # Create tutorial steps for the first tutorial
        if created_tutorials:
            first_tutorial = created_tutorials[0]
            steps_data = [
                {
                    'title': 'Introduction to Financial Markets',
                    'content': 'Welcome to your first lesson! In this step, you\'ll learn about the different types of financial markets.',
                    'step_type': 'content',
                    'order': 1
                },
                {
                    'title': 'Understanding Market Participants',
                    'content': 'Learn about who participates in financial markets and their roles.',
                    'step_type': 'content',
                    'order': 2
                },
                {
                    'title': 'Knowledge Check',
                    'content': 'Test your understanding of the concepts covered so far.',
                    'step_type': 'quiz',
                    'order': 3
                }
            ]
            
            for step_data in steps_data:
                step_data['tutorial'] = first_tutorial
                step, created = TutorialStep.objects.get_or_create(
                    title=step_data['title'],
                    tutorial=first_tutorial,
                    order=step_data['order'],
                    defaults=step_data
                )
                if created:
                    self.stdout.write(f'Created tutorial step: {step.title}')
        
        # Create a sample quiz
        quiz_data = {
            'title': 'Trading Basics Quiz',
            'description': 'Test your knowledge of basic trading concepts.',
            'tutorial': created_tutorials[0] if created_tutorials else None,
            'passing_score': 70,
            'max_attempts': 3,
            'is_published': True,
            'created_by': admin_user
        }
        
        quiz, created = Quiz.objects.get_or_create(
            title=quiz_data['title'],
            defaults=quiz_data
        )
        
        if created:
            self.stdout.write(f'Created quiz: {quiz.title}')
            
            # Create quiz questions
            questions_data = [
                {
                    'question_text': 'What is the primary goal of trading?',
                    'question_type': 'multiple_choice',
                    'points': 1,
                    'order': 1,
                    'choices': [
                        ('To make a profit', True),
                        ('To help companies', False),
                        ('To provide liquidity', False),
                        ('To support the economy', False)
                    ]
                },
                {
                    'question_text': 'Which order type guarantees execution but not price?',
                    'question_type': 'multiple_choice',
                    'points': 1,
                    'order': 2,
                    'choices': [
                        ('Limit Order', False),
                        ('Market Order', True),
                        ('Stop Order', False),
                        ('Stop-Limit Order', False)
                    ]
                },
                {
                    'question_text': 'Day trading involves holding positions for more than one day.',
                    'question_type': 'true_false',
                    'points': 1,
                    'order': 3,
                    'choices': [
                        ('True', False),
                        ('False', True)
                    ]
                }
            ]
            
            for q_data in questions_data:
                choices = q_data.pop('choices')
                q_data['quiz'] = quiz
                
                question, created = QuizQuestion.objects.get_or_create(
                    question_text=q_data['question_text'],
                    quiz=quiz,
                    defaults=q_data
                )
                
                if created:
                    for choice_text, is_correct in choices:
                        QuizChoice.objects.create(
                            question=question,
                            choice_text=choice_text,
                            is_correct=is_correct
                        )
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created sample educational content!')
        )
