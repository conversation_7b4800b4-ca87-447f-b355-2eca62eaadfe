"""
Performance monitoring and optimization views.
"""

import logging
from django.shortcuts import render
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse
from django.utils import timezone
from django.core.cache import cache
from django.db import connection
from apps.trading.optimization import DatabaseOptimizer, PerformanceMonitor, QueryOptimizer
from apps.trading.cache import CacheManager, MarketDataCache, PortfolioCache

logger = logging.getLogger(__name__)


def is_staff_user(user):
    """Check if user is staff."""
    return user.is_staff


@login_required
@user_passes_test(is_staff_user)
def performance_dashboard(request):
    """Performance monitoring dashboard."""
    context = {
        'title': 'Performance Dashboard',
        'current_time': timezone.now(),
    }
    
    try:
        # Get database metrics
        db_metrics = PerformanceMonitor.get_database_metrics()
        context['db_metrics'] = db_metrics
        
        # Get cache statistics
        cache_stats = get_cache_statistics()
        context['cache_stats'] = cache_stats
        
        # Get query performance analysis
        query_analysis = DatabaseOptimizer.analyze_query_performance()
        context['query_analysis'] = query_analysis
        
    except Exception as e:
        logger.error(f"Error loading performance dashboard: {str(e)}")
        context['error'] = str(e)
    
    return render(request, 'trading/performance/dashboard.html', context)


@login_required
@user_passes_test(is_staff_user)
def database_optimization_api(request):
    """API endpoint for database optimization operations."""
    if request.method == 'POST':
        operation = request.POST.get('operation')
        
        try:
            if operation == 'analyze':
                result = DatabaseOptimizer.analyze_query_performance()
                return JsonResponse({
                    'success': True,
                    'data': result,
                    'message': 'Database analysis completed'
                })
            
            elif operation == 'optimize':
                success = DatabaseOptimizer.optimize_database()
                return JsonResponse({
                    'success': success,
                    'message': 'Database optimization completed' if success else 'Optimization failed'
                })
            
            elif operation == 'cleanup':
                days_to_keep = int(request.POST.get('days_to_keep', 90))
                cleanup_stats = PerformanceMonitor.cleanup_old_data(days_to_keep)
                return JsonResponse({
                    'success': True,
                    'data': cleanup_stats,
                    'message': f'Data cleanup completed, kept {days_to_keep} days of data'
                })
            
            elif operation == 'metrics':
                metrics = PerformanceMonitor.get_database_metrics()
                return JsonResponse({
                    'success': True,
                    'data': metrics,
                    'message': 'Database metrics retrieved'
                })
            
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'Invalid operation'
                }, status=400)
                
        except Exception as e:
            logger.error(f"Database optimization API error: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=500)
    
    return JsonResponse({
        'success': False,
        'message': 'Method not allowed'
    }, status=405)


@login_required
@user_passes_test(is_staff_user)
def cache_management_api(request):
    """API endpoint for cache management operations."""
    if request.method == 'POST':
        operation = request.POST.get('operation')
        
        try:
            if operation == 'clear_all':
                cache.clear()
                return JsonResponse({
                    'success': True,
                    'message': 'All cache cleared'
                })
            
            elif operation == 'clear_user':
                user_id = request.POST.get('user_id')
                if user_id:
                    deleted_count = CacheManager.invalidate_user_cache(user_id)
                    return JsonResponse({
                        'success': True,
                        'data': {'deleted_count': deleted_count},
                        'message': f'User cache cleared for user {user_id}'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'User ID required'
                    }, status=400)
            
            elif operation == 'clear_account':
                account_id = request.POST.get('account_id')
                if account_id:
                    deleted_count = CacheManager.invalidate_account_cache(account_id)
                    return JsonResponse({
                        'success': True,
                        'data': {'deleted_count': deleted_count},
                        'message': f'Account cache cleared for account {account_id}'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'Account ID required'
                    }, status=400)
            
            elif operation == 'warm_cache':
                # Warm cache for popular data
                success = warm_popular_cache()
                return JsonResponse({
                    'success': success,
                    'message': 'Cache warming completed' if success else 'Cache warming failed'
                })
            
            elif operation == 'stats':
                stats = get_cache_statistics()
                return JsonResponse({
                    'success': True,
                    'data': stats,
                    'message': 'Cache statistics retrieved'
                })
            
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'Invalid operation'
                }, status=400)
                
        except Exception as e:
            logger.error(f"Cache management API error: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=500)
    
    return JsonResponse({
        'success': False,
        'message': 'Method not allowed'
    }, status=405)


@login_required
@user_passes_test(is_staff_user)
def query_performance_api(request):
    """API endpoint for query performance monitoring."""
    if request.method == 'GET':
        try:
            # Get slow query information
            slow_queries = get_slow_query_info()
            
            # Get query optimization suggestions
            optimization_suggestions = get_optimization_suggestions()
            
            return JsonResponse({
                'success': True,
                'data': {
                    'slow_queries': slow_queries,
                    'optimization_suggestions': optimization_suggestions,
                    'timestamp': timezone.now().isoformat()
                },
                'message': 'Query performance data retrieved'
            })
            
        except Exception as e:
            logger.error(f"Query performance API error: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=500)
    
    return JsonResponse({
        'success': False,
        'message': 'Method not allowed'
    }, status=405)


def get_cache_statistics():
    """Get cache statistics."""
    stats = {
        'default': {'status': 'unknown'},
        'market_data': {'status': 'unknown'},
        'sessions': {'status': 'unknown'}
    }
    
    try:
        # Test default cache
        test_key = 'cache_test'
        cache.set(test_key, 'test_value', 10)
        if cache.get(test_key) == 'test_value':
            stats['default']['status'] = 'working'
            cache.delete(test_key)
        else:
            stats['default']['status'] = 'error'
            
        # Get cache backend info
        stats['default']['backend'] = cache.__class__.__name__
        
    except Exception as e:
        stats['default']['status'] = 'error'
        stats['default']['error'] = str(e)
    
    return stats


def warm_popular_cache():
    """Warm cache with popular data."""
    try:
        # Warm market data cache
        popular_symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA']
        MarketDataCache.get_latest_prices(popular_symbols)
        
        # Warm portfolio cache for active accounts
        from apps.trading.models import Account
        active_accounts = Account.objects.filter(status='active')[:5]
        
        for account in active_accounts:
            PortfolioCache.get_portfolio_summary(account.id)
            PortfolioCache.get_trading_statistics(account.id)
        
        return True
        
    except Exception as e:
        logger.error(f"Cache warming error: {str(e)}")
        return False


def get_slow_query_info():
    """Get information about slow queries."""
    # This is a placeholder - in production you'd integrate with query monitoring tools
    return [
        {
            'query': 'SELECT * FROM trading_orders WHERE status = ?',
            'avg_duration_ms': 150,
            'count': 25,
            'suggestion': 'Add index on status column'
        },
        {
            'query': 'SELECT * FROM market_data WHERE symbol_id = ? AND is_latest = ?',
            'avg_duration_ms': 80,
            'count': 100,
            'suggestion': 'Composite index already exists'
        }
    ]


def get_optimization_suggestions():
    """Get query optimization suggestions."""
    suggestions = []
    
    with connection.cursor() as cursor:
        # Check for tables without indexes
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            AND name NOT LIKE 'django_%'
            AND name NOT LIKE 'auth_%'
            AND name NOT LIKE 'cache_%'
        """)
        
        tables = [row[0] for row in cursor.fetchall()]
        
        for table in tables:
            try:
                cursor.execute(f"PRAGMA index_list({table})")
                indexes = cursor.fetchall()
                
                if len(indexes) < 2:  # Only primary key
                    suggestions.append({
                        'type': 'missing_indexes',
                        'table': table,
                        'suggestion': f'Consider adding indexes to {table} table',
                        'priority': 'medium'
                    })
            except Exception:
                continue
    
    return suggestions
