{% extends 'base.html' %}
{% load static %}

{% block title %}Portfolio Overview - Trading Simulator{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item">
    <a href="{% url 'trading:account_overview' %}" class="text-decoration-none">
        <i class="fas fa-wallet me-1"></i>Accounts
    </a>
</li>
<li class="breadcrumb-item active" aria-current="page">
    <i class="fas fa-chart-pie me-1"></i>Portfolio
</li>
{% endblock %}

{% block extra_css %}
<style>
    .portfolio-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    
    .portfolio-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 15px;
        transition: transform 0.3s ease;
    }
    
    .portfolio-card:hover {
        transform: translateY(-5px);
    }
    
    .performance-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
    }
    
    .allocation-chart {
        max-height: 400px;
    }
    
    .positive-value {
        color: #28a745;
    }
    
    .negative-value {
        color: #dc3545;
    }
    
    .neutral-value {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 col-lg-2 dashboard-sidebar p-0">
            <div class="p-3">
                <div class="sidebar-section-title">Trading</div>
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="{% url 'trading:account_overview' %}" class="nav-link">
                            <i class="fas fa-wallet"></i>
                            Accounts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:portfolio_overview' %}" class="nav-link active">
                            <i class="fas fa-chart-pie"></i>
                            Portfolio
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:virtual_deposit' %}" class="nav-link">
                            <i class="fas fa-plus-circle"></i>
                            Deposit Funds
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 p-4">
            <!-- Header -->
            <div class="portfolio-header p-4 mb-4">
                <div class="text-center">
                    <i class="fas fa-chart-pie fa-3x mb-3"></i>
                    <h1>Portfolio Overview</h1>
                    <p class="mb-0">Track your investment performance across all accounts</p>
                </div>
            </div>
            
            <!-- Summary Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card performance-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-2x mb-3"></i>
                            <h3>${{ total_portfolio_value|floatformat:2 }}</h3>
                            <p class="mb-0">Total Portfolio Value</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card performance-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-money-bill-wave fa-2x mb-3"></i>
                            <h3>${{ total_cash_value|floatformat:2 }}</h3>
                            <p class="mb-0">Total Cash</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card performance-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-bar fa-2x mb-3"></i>
                            <h3>${{ total_positions_value|floatformat:2 }}</h3>
                            <p class="mb-0">Total Positions</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card performance-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-percentage fa-2x mb-3"></i>
                            <h3 class="{% if total_unrealized_pnl >= 0 %}text-light{% else %}text-warning{% endif %}">
                                {% if total_unrealized_pnl >= 0 %}+{% endif %}${{ total_unrealized_pnl|floatformat:2 }}
                            </h3>
                            <p class="mb-0">Unrealized P&L</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Portfolio Cards -->
            <div class="row">
                {% for portfolio in portfolios %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card portfolio-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h5 class="card-title mb-0">{{ portfolio.account.name }}</h5>
                                <span class="badge bg-light text-dark">
                                    {{ portfolio.account.get_account_type_display }}
                                </span>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Total Value:</span>
                                    <strong>${{ portfolio.total_portfolio_value|floatformat:2 }}</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Cash:</span>
                                    <span>${{ portfolio.cash_value|floatformat:2 }}</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Positions:</span>
                                    <span>${{ portfolio.total_value|floatformat:2 }}</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Unrealized P&L:</span>
                                    <span class="{% if portfolio.unrealized_pnl >= 0 %}text-success{% else %}text-danger{% endif %}">
                                        {% if portfolio.unrealized_pnl >= 0 %}+{% endif %}${{ portfolio.unrealized_pnl|floatformat:2 }}
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Total Return:</span>
                                    <strong class="{% if portfolio.total_return >= 0 %}text-success{% else %}text-danger{% endif %}">
                                        {% if portfolio.total_return >= 0 %}+{% endif %}{{ portfolio.total_return|floatformat:2 }}%
                                    </strong>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-light opacity-75">
                                    <i class="fas fa-layer-group me-1"></i>
                                    {{ portfolio.position_count }} position{{ portfolio.position_count|pluralize }}
                                </small>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="{% url 'trading:portfolio_detail' portfolio.account.id %}" class="btn btn-light btn-sm">
                                    <i class="fas fa-eye me-1"></i>View Portfolio
                                </a>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0">
                            <small class="opacity-75">
                                <i class="fas fa-clock me-1"></i>
                                Updated: {{ portfolio.updated_at|timesince }} ago
                            </small>
                        </div>
                    </div>
                </div>
                {% empty %}
                <!-- No Portfolios Message -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <h4>No Portfolios Yet</h4>
                            <p class="text-muted mb-4">
                                Create a trading account to start building your portfolio.
                            </p>
                            <a href="{% url 'trading:create_account' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Trading Account
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Performance Chart Section -->
            {% if performance_data %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>Portfolio Performance (30 Days)
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="performanceChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/websocket-manager.js' %}"></script>
<script src="{% static 'js/trading-websocket.js' %}"></script>

{% if performance_data %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Performance Chart
const performanceData = {{ performance_data|safe }};
const ctx = document.getElementById('performanceChart').getContext('2d');

new Chart(ctx, {
    type: 'line',
    data: {
        labels: performanceData.map(d => new Date(d.date).toLocaleDateString()),
        datasets: [{
            label: 'Portfolio Value',
            data: performanceData.map(d => d.total_value),
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: false,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return 'Portfolio Value: $' + context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});
</script>
{% endif %}

<script>
// Initialize WebSocket connections for real-time updates
document.addEventListener('DOMContentLoaded', function() {
    {% if portfolios %}
    // Initialize WebSocket for the first portfolio (or primary account)
    {% for portfolio in portfolios %}
    {% if forloop.first %}
    const tradingWS = initializeTradingWebSocket('{{ portfolio.account.id }}');

    // Set up update callbacks
    tradingWS.onUpdate('portfolio', function(data) {
        console.log('Portfolio updated via WebSocket:', data);
        updatePortfolioCards(data);
    });

    tradingWS.onUpdate('price', function(data) {
        console.log('Price updated via WebSocket:', data);
        // Update any price displays
        updatePriceDisplays(data);
    });
    {% endif %}
    {% endfor %}

    // Connection status indicator
    let connectionStatus = document.createElement('div');
    connectionStatus.id = 'connection-status';
    connectionStatus.className = 'position-fixed top-0 end-0 m-3 p-2 rounded bg-success text-white';
    connectionStatus.style.zIndex = '9999';
    connectionStatus.innerHTML = '<i class="fas fa-wifi me-2"></i>Live Updates';
    document.body.appendChild(connectionStatus);

    // Listen for WebSocket events
    document.addEventListener('websocket-connected', function(event) {
        if (event.detail.name === 'portfolio') {
            connectionStatus.className = 'position-fixed top-0 end-0 m-3 p-2 rounded bg-success text-white';
            connectionStatus.innerHTML = '<i class="fas fa-wifi me-2"></i>Live Updates';
        }
    });

    document.addEventListener('websocket-disconnected', function(event) {
        if (event.detail.name === 'portfolio') {
            connectionStatus.className = 'position-fixed top-0 end-0 m-3 p-2 rounded bg-warning text-dark';
            connectionStatus.innerHTML = '<i class="fas fa-wifi me-2"></i>Reconnecting...';
        }
    });

    document.addEventListener('websocket-error', function(event) {
        if (event.detail.name === 'portfolio') {
            connectionStatus.className = 'position-fixed top-0 end-0 m-3 p-2 rounded bg-danger text-white';
            connectionStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Connection Error';
        }
    });
    {% endif %}
});

function updatePortfolioCards(data) {
    // Update portfolio card values with real-time data
    if (data && data.account_id) {
        // Find the portfolio card for this account
        const portfolioCards = document.querySelectorAll('.portfolio-card');
        portfolioCards.forEach(card => {
            // Update values if this is the matching account
            // This is a simplified update - in a real implementation,
            // you'd match by account ID and update specific values
            console.log('Updating portfolio card for account:', data.account_id);
        });
    }
}

function updatePriceDisplays(priceData) {
    // Update price displays with animation
    const priceElements = document.querySelectorAll(`[data-symbol="${priceData.symbol}"] .current-price`);
    priceElements.forEach(element => {
        const oldPrice = parseFloat(element.textContent.replace('$', ''));
        const newPrice = priceData.price;

        element.textContent = `$${newPrice.toFixed(2)}`;

        // Add price change animation
        if (newPrice > oldPrice) {
            element.classList.add('price-up');
        } else if (newPrice < oldPrice) {
            element.classList.add('price-down');
        }

        setTimeout(() => {
            element.classList.remove('price-up', 'price-down');
        }, 2000);
    });
}

// Fallback refresh every 5 minutes if WebSocket fails
setInterval(() => {
    const statuses = window.wsManager ? window.wsManager.getAllStatuses() : {};
    const portfolioConnected = statuses.portfolio && statuses.portfolio.isConnected;

    if (!portfolioConnected) {
        console.log('WebSocket not connected, refreshing page');
        location.reload();
    }
}, 300000); // 5 minutes
</script>

<style>
.price-up {
    background-color: #d4edda !important;
    color: #155724 !important;
    transition: all 0.3s ease;
}

.price-down {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    transition: all 0.3s ease;
}

.price-updated {
    animation: priceFlash 1s ease-in-out;
}

@keyframes priceFlash {
    0% { background-color: #007bff; color: white; }
    50% { background-color: #007bff; color: white; }
    100% { background-color: transparent; color: inherit; }
}

#connection-status {
    font-size: 0.875rem;
    transition: all 0.3s ease;
}
</style>
{% endblock %}
