"""
Database optimization utilities and query optimization helpers.
"""

import logging
from django.db import connection, transaction
from django.db.models import Prefetch, Q, F, Count, Sum, Avg, Max, Min
from django.core.cache import cache
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from .models import Account, Portfolio, Position, Order, Trade, PortfolioHistory
from apps.market_data.models import Symbol, MarketData

logger = logging.getLogger(__name__)


class DatabaseOptimizer:
    """Database optimization utilities."""
    
    @staticmethod
    def analyze_query_performance():
        """Analyze database query performance and suggest optimizations."""
        with connection.cursor() as cursor:
            # Get query statistics (SQLite specific)
            cursor.execute("PRAGMA compile_options;")
            compile_options = cursor.fetchall()
            
            cursor.execute("PRAGMA cache_size;")
            cache_size = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size;")
            page_size = cursor.fetchone()[0]
            
            return {
                'compile_options': compile_options,
                'cache_size': cache_size,
                'page_size': page_size,
                'recommendations': DatabaseOptimizer._get_optimization_recommendations()
            }
    
    @staticmethod
    def _get_optimization_recommendations():
        """Get database optimization recommendations."""
        recommendations = []
        
        # Check for missing indexes
        with connection.cursor() as cursor:
            # Check if our performance indexes exist
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND name LIKE 'idx_%'
            """)
            indexes = [row[0] for row in cursor.fetchall()]
            
            expected_indexes = [
                'idx_accounts_user_status',
                'idx_orders_account_status',
                'idx_market_data_symbol_latest',
                'idx_positions_portfolio_symbol'
            ]
            
            missing_indexes = [idx for idx in expected_indexes if idx not in indexes]
            if missing_indexes:
                recommendations.append({
                    'type': 'missing_indexes',
                    'message': f'Missing performance indexes: {", ".join(missing_indexes)}',
                    'action': 'Run database migrations to add missing indexes'
                })
        
        return recommendations
    
    @staticmethod
    def optimize_database():
        """Perform database optimization operations."""
        with connection.cursor() as cursor:
            # Analyze database
            cursor.execute("ANALYZE;")
            
            # Vacuum database to reclaim space
            cursor.execute("VACUUM;")
            
            # Update statistics
            cursor.execute("PRAGMA optimize;")
            
        logger.info("Database optimization completed")
        return True


class QueryOptimizer:
    """Query optimization utilities for common trading operations."""
    
    @staticmethod
    def get_optimized_portfolio_data(account_id):
        """Get portfolio data with optimized queries."""
        cache_key = f"portfolio_data_{account_id}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # Optimized query with select_related and prefetch_related
        portfolio = Portfolio.objects.select_related('account').prefetch_related(
            Prefetch(
                'positions',
                queryset=Position.objects.filter(quantity__gt=0).select_related('portfolio')
            )
        ).get(account_id=account_id)
        
        # Get latest market data for all positions in one query
        symbols = [pos.symbol for pos in portfolio.positions.all()]
        if symbols:
            market_data = MarketData.objects.filter(
                symbol__symbol__in=symbols,
                is_latest=True
            ).select_related('symbol')
            
            # Create a mapping for quick lookup
            price_map = {md.symbol.symbol: md.price for md in market_data}
            
            # Update position current prices
            for position in portfolio.positions.all():
                if position.symbol in price_map:
                    position.current_price = price_map[position.symbol]
        
        # Cache for 30 seconds
        cache.set(cache_key, portfolio, 30)
        return portfolio
    
    @staticmethod
    def get_optimized_order_history(account_id, limit=50):
        """Get order history with optimized queries."""
        cache_key = f"order_history_{account_id}_{limit}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # Optimized query with select_related
        orders = Order.objects.filter(
            account_id=account_id
        ).select_related('account').prefetch_related(
            Prefetch(
                'trades',
                queryset=Trade.objects.select_related('order')
            )
        ).order_by('-created_at')[:limit]
        
        # Cache for 60 seconds
        cache.set(cache_key, list(orders), 60)
        return orders
    
    @staticmethod
    def get_optimized_market_data(symbols, limit=100):
        """Get market data with optimized queries."""
        if not symbols:
            return MarketData.objects.none()
        
        cache_key = f"market_data_{'_'.join(sorted(symbols))}_{limit}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # Optimized query with select_related
        market_data = MarketData.objects.filter(
            symbol__symbol__in=symbols,
            is_latest=True
        ).select_related('symbol').order_by('-timestamp')[:limit]
        
        # Cache for 15 seconds (market data changes frequently)
        cache.set(cache_key, list(market_data), 15)
        return market_data
    
    @staticmethod
    def get_portfolio_performance_data(account_id, days=30):
        """Get portfolio performance data with optimized queries."""
        cache_key = f"portfolio_performance_{account_id}_{days}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # Calculate date range
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Optimized query with date filtering
        performance_data = PortfolioHistory.objects.filter(
            portfolio__account_id=account_id,
            date__gte=start_date,
            date__lte=end_date
        ).select_related('portfolio').order_by('date')
        
        # Cache for 5 minutes
        cache.set(cache_key, list(performance_data), 300)
        return performance_data
    
    @staticmethod
    def get_trading_statistics(account_id):
        """Get trading statistics with optimized aggregation queries."""
        cache_key = f"trading_stats_{account_id}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # Optimized aggregation queries
        order_stats = Order.objects.filter(account_id=account_id).aggregate(
            total_orders=Count('id'),
            filled_orders=Count('id', filter=Q(status='filled')),
            pending_orders=Count('id', filter=Q(status__in=['pending', 'partially_filled'])),
            cancelled_orders=Count('id', filter=Q(status='cancelled')),
            avg_order_size=Avg('quantity'),
            total_volume=Sum('quantity', filter=Q(status='filled'))
        )
        
        trade_stats = Trade.objects.filter(order__account_id=account_id).aggregate(
            total_trades=Count('id'),
            total_volume=Sum('quantity'),
            avg_trade_size=Avg('net_amount'),
            total_commission=Sum('commission')
        )
        
        # Combine statistics
        stats = {
            **order_stats,
            **trade_stats,
            'calculated_at': timezone.now()
        }
        
        # Cache for 2 minutes
        cache.set(cache_key, stats, 120)
        return stats


class PerformanceMonitor:
    """Performance monitoring utilities."""
    
    @staticmethod
    def monitor_slow_queries(threshold_ms=1000):
        """Monitor and log slow queries."""
        # This would typically integrate with Django Debug Toolbar or similar
        # For now, we'll create a simple monitoring framework
        
        class SlowQueryLogger:
            def __init__(self, threshold):
                self.threshold = threshold
                
            def __enter__(self):
                self.start_time = timezone.now()
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                duration = (timezone.now() - self.start_time).total_seconds() * 1000
                if duration > self.threshold:
                    logger.warning(f"Slow query detected: {duration:.2f}ms")
        
        return SlowQueryLogger(threshold_ms)
    
    @staticmethod
    def get_database_metrics():
        """Get database performance metrics."""
        with connection.cursor() as cursor:
            # Get database size
            cursor.execute("PRAGMA page_count;")
            page_count = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size;")
            page_size = cursor.fetchone()[0]
            
            db_size = page_count * page_size
            
            # Get table statistics
            cursor.execute("""
                SELECT name, COUNT(*) as row_count
                FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                GROUP BY name
            """)
            
            table_stats = {}
            for table_name, row_count in cursor.fetchall():
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    actual_count = cursor.fetchone()[0]
                    table_stats[table_name] = actual_count
                except Exception:
                    table_stats[table_name] = 0
            
            return {
                'database_size_bytes': db_size,
                'database_size_mb': round(db_size / (1024 * 1024), 2),
                'table_statistics': table_stats,
                'total_records': sum(table_stats.values())
            }
    
    @staticmethod
    def cleanup_old_data(days_to_keep=90):
        """Clean up old data to improve performance."""
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        
        cleanup_stats = {
            'portfolio_history': 0,
            'market_data': 0,
            'old_trades': 0
        }
        
        with transaction.atomic():
            # Clean up old portfolio history (keep daily snapshots)
            old_portfolio_history = PortfolioHistory.objects.filter(
                date__lt=cutoff_date.date()
            )
            cleanup_stats['portfolio_history'] = old_portfolio_history.count()
            old_portfolio_history.delete()
            
            # Clean up old market data (keep only latest for each symbol)
            old_market_data = MarketData.objects.filter(
                timestamp__lt=cutoff_date,
                is_latest=False
            )
            cleanup_stats['market_data'] = old_market_data.count()
            old_market_data.delete()
            
            # Archive very old completed trades
            very_old_date = timezone.now() - timedelta(days=days_to_keep * 2)
            old_trades = Trade.objects.filter(
                executed_at__lt=very_old_date
            )
            cleanup_stats['old_trades'] = old_trades.count()
            # Note: In production, you might want to archive rather than delete
            
        logger.info(f"Data cleanup completed: {cleanup_stats}")
        return cleanup_stats
