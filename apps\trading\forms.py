from django import forms
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from decimal import Decimal
from .models import Account, Transaction, Order
from apps.market_data.models import Symbol, MarketData


class VirtualDepositForm(forms.Form):
    """
    Form for virtual deposits to trading accounts.
    """
    
    account = forms.ModelChoiceField(
        queryset=Account.objects.none(),  # Will be set in __init__
        widget=forms.Select(attrs={
            'class': 'form-select',
            'id': 'id_account'
        }),
        help_text='Select the account to deposit funds into'
    )
    
    amount = forms.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal('10.00'), message='Minimum deposit amount is $10.00'),
            MaxValueValidator(Decimal('100000.00'), message='Maximum deposit amount is $100,000.00')
        ],
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0.00',
            'step': '0.01',
            'min': '10.00',
            'max': '100000.00'
        }),
        help_text='Enter the amount to deposit (minimum $10.00)'
    )
    
    description = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Optional description for this deposit'
        }),
        help_text='Optional description for this deposit'
    )
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            # Only show active accounts for the current user
            self.fields['account'].queryset = Account.objects.filter(
                user=user, 
                status='active'
            ).order_by('account_type', 'name')
    
    def clean_amount(self):
        """Validate deposit amount."""
        amount = self.cleaned_data.get('amount')
        
        if amount is None:
            raise forms.ValidationError('Please enter a valid amount.')
        
        if amount <= 0:
            raise forms.ValidationError('Deposit amount must be positive.')
        
        return amount
    
    def clean(self):
        """Validate the entire form and check account-specific limits."""
        cleaned_data = super().clean()
        account = cleaned_data.get('account')
        amount = cleaned_data.get('amount')
        
        if account and amount:
            # Check if deposit is allowed for this account
            can_deposit, message = account.can_deposit(amount)
            if not can_deposit:
                raise forms.ValidationError(message)
        
        return cleaned_data


class AccountCreationForm(forms.ModelForm):
    """
    Form for creating new trading accounts.
    """
    
    class Meta:
        model = Account
        fields = ['name', 'description', 'account_type', 'initial_balance']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter account name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Optional description of account purpose'
            }),
            'account_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'initial_balance': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '1000.00',
                'max': '1000000.00'
            })
        }
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
        
        # Set help text
        self.fields['name'].help_text = 'Choose a name for your trading account'
        self.fields['description'].help_text = 'Optional description of what you plan to use this account for'
        self.fields['account_type'].help_text = 'Select the type of account based on your trading goals'
        self.fields['initial_balance'].help_text = 'Starting balance for your account (minimum $1,000)'
    
    def clean_name(self):
        """Validate account name uniqueness for the user."""
        name = self.cleaned_data.get('name')
        
        if self.user and Account.objects.filter(user=self.user, name=name).exists():
            raise forms.ValidationError('You already have an account with this name.')
        
        return name
    
    def save(self, commit=True):
        """Save the account with the current user."""
        account = super().save(commit=False)
        if self.user:
            account.user = self.user
        
        if commit:
            account.save()
        
        return account


class AccountUpdateForm(forms.ModelForm):
    """
    Form for updating existing trading accounts.
    """
    
    class Meta:
        model = Account
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3
            })
        }
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
    
    def clean_name(self):
        """Validate account name uniqueness for the user."""
        name = self.cleaned_data.get('name')
        
        if self.user and self.instance:
            # Check if another account with this name exists (excluding current account)
            existing = Account.objects.filter(
                user=self.user, 
                name=name
            ).exclude(id=self.instance.id)
            
            if existing.exists():
                raise forms.ValidationError('You already have an account with this name.')
        
        return name


class TransactionFilterForm(forms.Form):
    """
    Form for filtering transaction history.
    """
    
    TRANSACTION_TYPE_CHOICES = [
        ('', 'All Types'),
    ] + Transaction.TRANSACTION_TYPE_CHOICES
    
    transaction_type = forms.ChoiceField(
        choices=TRANSACTION_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    def clean(self):
        """Validate date range."""
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise forms.ValidationError('Start date must be before end date.')
        
        return cleaned_data


class OrderForm(forms.ModelForm):
    """
    Form for creating and editing orders.
    """

    symbol_search = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter symbol (e.g., AAPL)',
            'autocomplete': 'off',
            'data-bs-toggle': 'tooltip',
            'title': 'Search for a stock symbol'
        }),
        help_text='Enter the stock symbol you want to trade'
    )

    class Meta:
        model = Order
        fields = [
            'symbol', 'order_type', 'side', 'quantity', 'price',
            'stop_price', 'trailing_amount', 'trailing_percent',
            'condition_symbol', 'condition_price', 'condition_operator',
            'time_in_force', 'notes'
        ]
        widgets = {
            'symbol': forms.HiddenInput(),
            'order_type': forms.Select(attrs={
                'class': 'form-select',
                'onchange': 'updatePriceFields()'
            }),
            'side': forms.Select(attrs={
                'class': 'form-select',
                'onchange': 'updateOrderPreview()'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'step': '1',
                'onchange': 'updateOrderPreview()'
            }),
            'price': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0.01',
                'step': '0.01',
                'onchange': 'updateOrderPreview()'
            }),
            'stop_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0.01',
                'step': '0.01'
            }),
            'trailing_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0.01',
                'step': '0.01'
            }),
            'trailing_percent': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0.01',
                'max': '50.00',
                'step': '0.01'
            }),
            'condition_symbol': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., SPY'
            }),
            'condition_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0.01',
                'step': '0.01'
            }),
            'condition_operator': forms.Select(attrs={
                'class': 'form-select'
            }),
            'time_in_force': forms.Select(attrs={
                'class': 'form-select'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Optional notes about this order...'
            })
        }

    def __init__(self, *args, **kwargs):
        self.account = kwargs.pop('account', None)
        self.symbol_obj = kwargs.pop('symbol_obj', None)
        super().__init__(*args, **kwargs)

        # Set initial symbol if provided
        if self.symbol_obj:
            self.fields['symbol'].initial = self.symbol_obj.symbol
            self.fields['symbol_search'].initial = f"{self.symbol_obj.symbol} - {self.symbol_obj.name}"
            self.fields['symbol_search'].widget.attrs['readonly'] = True

        # Customize field labels and help text
        self.fields['order_type'].help_text = 'Market orders execute immediately at current price'
        self.fields['quantity'].help_text = 'Number of shares to trade'
        self.fields['price'].help_text = 'Limit price (required for limit orders)'
        self.fields['stop_price'].help_text = 'Stop price (required for stop orders)'
        self.fields['trailing_amount'].help_text = 'Trailing amount in dollars (for trailing stop orders)'
        self.fields['trailing_percent'].help_text = 'Trailing percentage (for trailing stop orders)'
        self.fields['condition_symbol'].help_text = 'Symbol to watch for conditional orders'
        self.fields['condition_price'].help_text = 'Trigger price for conditional orders'
        self.fields['condition_operator'].help_text = 'Condition operator for conditional orders'

    def clean_symbol_search(self):
        """Validate and extract symbol from search field."""
        symbol_search = self.cleaned_data.get('symbol_search', '').strip().upper()

        if not symbol_search:
            raise ValidationError("Please enter a stock symbol")

        # Extract symbol (take first word before any space or dash)
        symbol = symbol_search.split()[0].split('-')[0].strip()

        # Validate symbol exists
        try:
            symbol_obj = Symbol.objects.get(symbol=symbol, is_active=True)
            if not symbol_obj.is_tradeable:
                raise ValidationError(f"Symbol {symbol} is not tradeable")
            return symbol
        except Symbol.DoesNotExist:
            raise ValidationError(f"Symbol {symbol} not found or not available for trading")

    def clean_quantity(self):
        """Validate quantity."""
        quantity = self.cleaned_data.get('quantity')

        if not quantity or quantity <= 0:
            raise ValidationError("Quantity must be a positive number")

        if quantity > 10000:
            raise ValidationError("Maximum quantity is 10,000 shares per order")

        return quantity

    def clean_price(self):
        """Validate price based on order type."""
        price = self.cleaned_data.get('price')
        order_type = self.cleaned_data.get('order_type')

        if order_type in ['limit', 'stop_limit']:
            if not price or price <= 0:
                raise ValidationError("Price is required for limit orders")

            if price > Decimal('10000.00'):
                raise ValidationError("Maximum price is $10,000 per share")

        elif order_type == 'market' and price:
            raise ValidationError("Market orders cannot have a limit price")

        return price

    def clean_stop_price(self):
        """Validate stop price based on order type."""
        stop_price = self.cleaned_data.get('stop_price')
        order_type = self.cleaned_data.get('order_type')

        if order_type in ['stop', 'stop_limit']:
            if not stop_price or stop_price <= 0:
                raise ValidationError("Stop price is required for stop orders")

            if stop_price > Decimal('10000.00'):
                raise ValidationError("Maximum stop price is $10,000 per share")

        elif order_type in ['market', 'limit'] and stop_price:
            raise ValidationError(f"{order_type.title()} orders cannot have a stop price")

        return stop_price

    def clean_trailing_amount(self):
        """Validate trailing amount for trailing stop orders."""
        trailing_amount = self.cleaned_data.get('trailing_amount')
        order_type = self.cleaned_data.get('order_type')

        if order_type == 'trailing_stop':
            trailing_percent = self.cleaned_data.get('trailing_percent')

            if not trailing_amount and not trailing_percent:
                raise ValidationError("Trailing stop orders require either trailing amount or trailing percentage")

            if trailing_amount and trailing_percent:
                raise ValidationError("Specify either trailing amount OR trailing percentage, not both")

            if trailing_amount and trailing_amount <= 0:
                raise ValidationError("Trailing amount must be positive")

        elif trailing_amount and order_type != 'trailing_stop':
            raise ValidationError("Trailing amount can only be used with trailing stop orders")

        return trailing_amount

    def clean_trailing_percent(self):
        """Validate trailing percentage for trailing stop orders."""
        trailing_percent = self.cleaned_data.get('trailing_percent')
        order_type = self.cleaned_data.get('order_type')

        if order_type == 'trailing_stop':
            if trailing_percent and (trailing_percent <= 0 or trailing_percent > 50):
                raise ValidationError("Trailing percentage must be between 0.01% and 50%")

        elif trailing_percent and order_type != 'trailing_stop':
            raise ValidationError("Trailing percentage can only be used with trailing stop orders")

        return trailing_percent

    def clean_condition_symbol(self):
        """Validate condition symbol for conditional orders."""
        condition_symbol = self.cleaned_data.get('condition_symbol', '').strip().upper()
        order_type = self.cleaned_data.get('order_type')

        if order_type == 'conditional':
            if not condition_symbol:
                raise ValidationError("Conditional orders require a condition symbol")

            # Validate symbol exists
            try:
                Symbol.objects.get(symbol=condition_symbol, is_active=True)
                return condition_symbol
            except Symbol.DoesNotExist:
                raise ValidationError(f"Condition symbol {condition_symbol} not found")

        elif condition_symbol and order_type != 'conditional':
            raise ValidationError("Condition symbol can only be used with conditional orders")

        return condition_symbol

    def clean_condition_price(self):
        """Validate condition price for conditional orders."""
        condition_price = self.cleaned_data.get('condition_price')
        order_type = self.cleaned_data.get('order_type')

        if order_type == 'conditional':
            if not condition_price or condition_price <= 0:
                raise ValidationError("Conditional orders require a valid condition price")

        elif condition_price and order_type != 'conditional':
            raise ValidationError("Condition price can only be used with conditional orders")

        return condition_price

    def clean(self):
        """Perform cross-field validation."""
        cleaned_data = super().clean()

        # Set symbol from symbol_search
        symbol_search = cleaned_data.get('symbol_search')
        if symbol_search:
            cleaned_data['symbol'] = symbol_search

        # Validate account has sufficient funds for buy orders
        if self.account:
            self.validate_account_balance(cleaned_data)

        return cleaned_data

    def validate_account_balance(self, cleaned_data):
        """Validate account has sufficient balance for buy orders."""
        side = cleaned_data.get('side')
        quantity = cleaned_data.get('quantity')
        order_type = cleaned_data.get('order_type')
        price = cleaned_data.get('price')

        if side == 'buy' and quantity and self.account:
            # Estimate order cost
            if order_type == 'market':
                # Use current market price for estimation
                symbol = cleaned_data.get('symbol')
                if symbol:
                    try:
                        symbol_obj = Symbol.objects.get(symbol=symbol)
                        latest_data = MarketData.objects.filter(
                            symbol=symbol_obj, is_latest=True
                        ).first()
                        estimated_price = latest_data.price if latest_data else Decimal('100.00')
                    except Symbol.DoesNotExist:
                        estimated_price = Decimal('100.00')
                else:
                    estimated_price = Decimal('100.00')
            else:
                estimated_price = price or Decimal('100.00')

            estimated_cost = Decimal(str(quantity)) * estimated_price + Decimal('1.00')  # Add commission

            if estimated_cost > self.account.current_balance:
                raise ValidationError(
                    f"Insufficient funds. Estimated cost: ${estimated_cost:,.2f}, "
                    f"Available balance: ${self.account.current_balance:,.2f}"
                )

    def save(self, commit=True):
        """Save the order with account assignment."""
        order = super().save(commit=False)

        if self.account:
            order.account = self.account

        if commit:
            order.save()

        return order


class QuickOrderForm(forms.Form):
    """
    Simplified form for quick market orders.
    """

    symbol = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'AAPL',
            'autocomplete': 'off'
        })
    )

    side = forms.ChoiceField(
        choices=Order.ORDER_SIDE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    quantity = forms.IntegerField(
        min_value=1,
        max_value=10000,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '1',
            'max': '10000'
        })
    )

    def __init__(self, *args, **kwargs):
        self.account = kwargs.pop('account', None)
        super().__init__(*args, **kwargs)

    def clean_symbol(self):
        """Validate symbol exists and is tradeable."""
        symbol = self.cleaned_data.get('symbol', '').strip().upper()

        try:
            symbol_obj = Symbol.objects.get(symbol=symbol, is_active=True)
            if not symbol_obj.is_tradeable:
                raise ValidationError(f"Symbol {symbol} is not tradeable")
            return symbol
        except Symbol.DoesNotExist:
            raise ValidationError(f"Symbol {symbol} not found")

    def create_order(self):
        """Create a market order from form data."""
        if not self.is_valid():
            return None

        order = Order(
            account=self.account,
            symbol=self.cleaned_data['symbol'],
            order_type='market',
            side=self.cleaned_data['side'],
            quantity=self.cleaned_data['quantity'],
            time_in_force='day'
        )

        order.save()
        return order


class BracketOrderForm(forms.Form):
    """
    Form for creating bracket orders (entry + stop loss + take profit).
    """

    symbol = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'AAPL',
            'autocomplete': 'off'
        })
    )

    side = forms.ChoiceField(
        choices=Order.ORDER_SIDE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    quantity = forms.IntegerField(
        min_value=1,
        max_value=10000,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '1',
            'max': '10000'
        })
    )

    entry_price = forms.DecimalField(
        max_digits=12,
        decimal_places=4,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '0.01',
            'step': '0.01'
        })
    )

    stop_loss_price = forms.DecimalField(
        max_digits=12,
        decimal_places=4,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '0.01',
            'step': '0.01'
        })
    )

    take_profit_price = forms.DecimalField(
        max_digits=12,
        decimal_places=4,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '0.01',
            'step': '0.01'
        })
    )

    def __init__(self, *args, **kwargs):
        self.account = kwargs.pop('account', None)
        super().__init__(*args, **kwargs)

    def clean_symbol(self):
        """Validate symbol exists and is tradeable."""
        symbol = self.cleaned_data.get('symbol', '').strip().upper()

        try:
            symbol_obj = Symbol.objects.get(symbol=symbol, is_active=True)
            if not symbol_obj.is_tradeable:
                raise ValidationError(f"Symbol {symbol} is not tradeable")
            return symbol
        except Symbol.DoesNotExist:
            raise ValidationError(f"Symbol {symbol} not found")

    def clean(self):
        """Validate bracket order logic."""
        cleaned_data = super().clean()

        side = cleaned_data.get('side')
        entry_price = cleaned_data.get('entry_price')
        stop_loss_price = cleaned_data.get('stop_loss_price')
        take_profit_price = cleaned_data.get('take_profit_price')

        if all([side, entry_price, stop_loss_price, take_profit_price]):
            if side == 'buy':
                # For buy orders: stop loss < entry < take profit
                if stop_loss_price >= entry_price:
                    raise ValidationError("Stop loss price must be below entry price for buy orders")
                if take_profit_price <= entry_price:
                    raise ValidationError("Take profit price must be above entry price for buy orders")
            else:
                # For sell orders: take profit < entry < stop loss
                if take_profit_price >= entry_price:
                    raise ValidationError("Take profit price must be below entry price for sell orders")
                if stop_loss_price <= entry_price:
                    raise ValidationError("Stop loss price must be above entry price for sell orders")

        return cleaned_data

    def create_bracket_order(self):
        """Create bracket order from form data."""
        if not self.is_valid():
            return None

        from .services import OrderExecutionService

        result = OrderExecutionService.create_bracket_order(
            account=self.account,
            symbol=self.cleaned_data['symbol'],
            quantity=self.cleaned_data['quantity'],
            entry_price=self.cleaned_data['entry_price'],
            stop_loss=self.cleaned_data['stop_loss_price'],
            take_profit=self.cleaned_data['take_profit_price']
        )

        return result
