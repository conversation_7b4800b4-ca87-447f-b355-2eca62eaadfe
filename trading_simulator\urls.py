"""

"""

from django.contrib import admin
from django.urls import path, include
from django.views.generic import RedirectView
from django.conf import settings

urlpatterns = [
    path("admin/", admin.site.urls),
    path("accounts/", include("apps.accounts.urls")),
    path("trading/", include("apps.trading.urls")),
    path("market/", include("apps.market_data.urls")),
    path("", include("django.contrib.auth.urls")),  # For password reset URLs
    path("", RedirectView.as_view(url="/accounts/dashboard/", permanent=False)),
]

# Add debug toolbar URLs in development
if settings.DEBUG:
    import debug_toolbar
    urlpatterns = [
        path('__debug__/', include(debug_toolbar.urls)),
    ] + urlpatterns
