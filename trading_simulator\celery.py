"""
Celery configuration for trading_simulator project.
"""

import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trading_simulator.settings.development')

app = Celery('trading_simulator')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery configuration
app.conf.update(
    # Task routing
    task_routes={
        'apps.market_data.tasks.*': {'queue': 'market_data'},
        'apps.trading.tasks.*': {'queue': 'trading'},
        'apps.accounts.tasks.*': {'queue': 'notifications'},
    },
    
    # Task serialization
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Task execution
    task_always_eager=False,
    task_eager_propagates=True,
    task_ignore_result=False,
    task_store_eager_result=True,
    
    # Worker configuration
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'fetch-market-data': {
            'task': 'apps.market_data.tasks.fetch_all_market_data',
            'schedule': 60.0,  # Every minute during market hours
        },
        'process-orders': {
            'task': 'apps.trading.tasks.process_pending_orders',
            'schedule': 30.0,  # Every 30 seconds
        },
        'process-advanced-orders': {
            'task': 'apps.trading.tasks.process_advanced_orders',
            'schedule': 15.0,  # Every 15 seconds
        },
        'update-portfolios': {
            'task': 'apps.trading.tasks.update_portfolio_values',
            'schedule': 120.0,  # Every 2 minutes
        },
        'send-risk-alerts': {
            'task': 'apps.trading.tasks.send_risk_alerts',
            'schedule': 300.0,  # Every 5 minutes
        },
        'cleanup-old-data': {
            'task': 'apps.market_data.tasks.cleanup_old_market_data',
            'schedule': 3600.0,  # Every hour
        },
    },
)


@app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup."""
    print(f'Request: {self.request!r}')
    return 'Debug task completed'
