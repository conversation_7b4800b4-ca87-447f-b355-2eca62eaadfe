{% extends 'base.html' %}

{% block title %}Dashboard - Trading Simulator{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
    }
    
    .welcome-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .stats-card {
        background: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
        height: 100%;
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .stats-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .stats-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .balance-positive { color: #27ae60; }
    .balance-negative { color: #e74c3c; }
    .balance-neutral { color: #3498db; }
    
    .quick-actions {
        background: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 0.5rem;
        text-decoration: none;
        color: #6c757d;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }
    
    .action-btn:hover {
        border-color: #3498db;
        color: #3498db;
        transform: translateY(-2px);
        text-decoration: none;
    }
    
    .action-btn i {
        font-size: 1.5rem;
        margin-right: 0.75rem;
    }
    
    .recent-activity {
        background: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .activity-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.25rem;
    }
    
    .activity-buy { background: rgba(39, 174, 96, 0.1); color: #27ae60; }
    .activity-sell { background: rgba(231, 76, 60, 0.1); color: #e74c3c; }
    .activity-info { background: rgba(52, 152, 219, 0.1); color: #3498db; }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }
    
    .activity-time {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .verification-alert {
        border-left: 4px solid #f39c12;
        background: rgba(243, 156, 18, 0.1);
    }

    .profile-completion-alert {
        border-left: 4px solid #007bff;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    }

    .profile-avatar-small {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .card-header.bg-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .card-header.bg-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    }

    .card-header.bg-info {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    }

    .progress {
        height: 8px;
        border-radius: 10px;
        background-color: #f8f9fa;
    }

    .progress-bar {
        border-radius: 10px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    /* Dashboard Sidebar Styles */
    .dashboard-sidebar {
        background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
        border-right: 1px solid #dee2e6;
        min-height: calc(100vh - 200px);
    }

    .sidebar-nav {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-nav .nav-item {
        margin-bottom: 0.5rem;
    }

    .sidebar-nav .nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: #495057;
        text-decoration: none;
        border-radius: 0.375rem;
        transition: all 0.3s ease;
    }

    .sidebar-nav .nav-link:hover {
        background-color: #e9ecef;
        color: #212529;
        transform: translateX(5px);
    }

    .sidebar-nav .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .sidebar-nav .nav-link i {
        width: 20px;
        margin-right: 0.75rem;
    }

    .sidebar-section-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin: 1.5rem 1rem 0.5rem;
    }

    @media (max-width: 768px) {
        .dashboard-sidebar {
            min-height: auto;
            border-right: none;
            border-bottom: 1px solid #dee2e6;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1><i class="fas fa-tachometer-alt me-3"></i>Trading Dashboard</h1>
                <p class="mb-0">Welcome back, {{ user.first_name|default:user.username }}! Ready to trade?</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="text-white-50">
                    <small>Last login: {{ user.last_login|date:"M d, Y H:i" }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 col-lg-2 dashboard-sidebar p-0">
            <div class="p-3">
                <div class="sidebar-section-title">Overview</div>
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="{% url 'accounts:dashboard' %}" class="nav-link active">
                            <i class="fas fa-tachometer-alt"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:portfolio_overview' %}" class="nav-link">
                            <i class="fas fa-chart-pie"></i>
                            Portfolio
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:account_overview' %}" class="nav-link">
                            <i class="fas fa-history"></i>
                            Trade History
                        </a>
                    </li>
                </ul>

                <div class="sidebar-section-title">Trading</div>
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="{% url 'trading:account_overview' %}" class="nav-link">
                            <i class="fas fa-wallet"></i>
                            Trading Accounts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:virtual_deposit' %}" class="nav-link">
                            <i class="fas fa-plus-circle"></i>
                            Deposit Funds
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:portfolio_overview' %}" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            Portfolio
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'trading:account_overview' %}" class="nav-link">
                            <i class="fas fa-exchange-alt"></i>
                            Quick Trade
                        </a>
                    </li>
                </ul>

                <div class="sidebar-section-title">Learning</div>
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="{% url 'education:home' %}" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            Tutorials
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'market_data:market_overview' %}" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            Market Data
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'education:learning_paths' %}" class="nav-link">
                            <i class="fas fa-book"></i>
                            Trading Guide
                        </a>
                    </li>
                </ul>

                <div class="sidebar-section-title">Account</div>
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="{% url 'accounts:profile' %}" class="nav-link">
                            <i class="fas fa-user"></i>
                            Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'accounts:trading_preferences' %}" class="nav-link">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 p-4">
    <!-- Email Verification Alert -->
    {% if not user.email_verified %}
    <div class="alert verification-alert alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Email Verification Required:</strong> 
        Please check your email and click the verification link to fully activate your account.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}
    
    <!-- Welcome Card -->
    <div class="welcome-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h3>Welcome to Your Trading Journey!</h3>
                <p class="mb-0">
                    You're all set up with a virtual balance of ${{ user.initial_balance|floatformat:2 }}. 
                    Start exploring the markets and practice your trading skills risk-free.
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <i class="fas fa-rocket" style="font-size: 4rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card text-center">
                <div class="stats-icon balance-neutral">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="stats-value balance-neutral">${{ user.initial_balance|floatformat:2 }}</div>
                <div class="stats-label">Virtual Balance</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card text-center">
                <div class="stats-icon text-success">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-value text-success">$0.00</div>
                <div class="stats-label">Total Profit/Loss</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card text-center">
                <div class="stats-icon text-info">
                    <i class="fas fa-briefcase"></i>
                </div>
                <div class="stats-value text-info">0</div>
                <div class="stats-label">Active Positions</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card text-center">
                <div class="stats-icon text-warning">
                    <i class="fas fa-history"></i>
                </div>
                <div class="stats-value text-warning">0</div>
                <div class="stats-label">Total Trades</div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Quick Actions -->
        <div class="col-md-4 mb-4">
            <div class="quick-actions">
                <h5 class="mb-3"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                
                <a href="{% url 'trading:account_overview' %}" class="action-btn">
                    <i class="fas fa-plus-circle"></i>
                    <div>
                        <div class="fw-bold">Start Trading</div>
                        <small class="text-muted">Buy or sell stocks</small>
                    </div>
                </a>
                
                <a href="{% url 'trading:portfolio_overview' %}" class="action-btn">
                    <i class="fas fa-chart-pie"></i>
                    <div>
                        <div class="fw-bold">View Portfolio</div>
                        <small class="text-muted">Check your holdings</small>
                    </div>
                </a>
                
                <a href="{% url 'market_data:market_overview' %}" class="action-btn">
                    <i class="fas fa-search"></i>
                    <div>
                        <div class="fw-bold">Market Research</div>
                        <small class="text-muted">Analyze stocks</small>
                    </div>
                </a>
                
                <a href="{% url 'education:home' %}" class="action-btn">
                    <i class="fas fa-graduation-cap"></i>
                    <div>
                        <div class="fw-bold">Learning Center</div>
                        <small class="text-muted">Improve your skills</small>
                    </div>
                </a>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="col-md-8 mb-4">
            <div class="recent-activity">
                <h5 class="mb-3"><i class="fas fa-clock me-2"></i>Recent Activity</h5>
                
                <div class="activity-item">
                    <div class="activity-icon activity-info">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Account Created</div>
                        <div class="activity-time">Welcome to Trading Simulator!</div>
                    </div>
                    <div class="activity-time">
                        {{ user.date_joined|date:"M d, Y" }}
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon activity-info">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Virtual Balance Added</div>
                        <div class="activity-time">${{ user.initial_balance|floatformat:2 }} virtual funds</div>
                    </div>
                    <div class="activity-time">
                        {{ user.date_joined|date:"M d, Y" }}
                    </div>
                </div>
                
                <div class="text-center py-3">
                    <p class="text-muted mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Start trading to see your activity here
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Profile Completion Section -->
    {% if not is_profile_complete %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info border-0" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-info-circle fa-2x text-primary"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-2">Complete Your Profile</h5>
                        <p class="mb-2">Your profile is {{ profile_completion }}% complete. Complete your profile to unlock all features and get personalized trading recommendations.</p>
                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: {{ profile_completion }}%"
                                 aria-valuenow="{{ profile_completion }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <a href="{% url 'accounts:profile_edit' %}" class="btn btn-primary btn-sm me-2">
                            <i class="fas fa-edit me-1"></i>Complete Profile
                        </a>
                        <a href="{% url 'accounts:profile' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-user me-1"></i>View Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- User Profile Summary -->
    <div class="row">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Profile Overview</h5>
                </div>
                <div class="card-body text-center">
                    <img src="{{ user.get_profile_picture_url }}" alt="Profile Picture"
                         class="rounded-circle mb-3" style="width: 80px; height: 80px; object-fit: cover;">
                    <h6 class="card-title">{{ user.display_name }}</h6>
                    <p class="text-muted small">{{ user.email }}</p>
                    <div class="mb-3">
                        <span class="badge bg-primary">{{ profile_completion }}% Complete</span>
                        {% if user.email_verified %}
                            <span class="badge bg-success">Verified</span>
                        {% else %}
                            <span class="badge bg-warning">Unverified</span>
                        {% endif %}
                    </div>
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:profile' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>View Profile
                        </a>
                        <a href="{% url 'accounts:profile_edit' %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Trading Profile</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Experience Level</small>
                        <div class="fw-bold">{{ user.get_trading_experience_display }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Risk Tolerance</small>
                        <div class="fw-bold">{{ user.get_risk_tolerance_display }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Initial Balance</small>
                        <div class="fw-bold text-success">${{ user.initial_balance|floatformat:2 }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Member Since</small>
                        <div class="fw-bold">{{ user.date_joined|date:"M d, Y" }}</div>
                    </div>
                    <div class="d-grid">
                        <a href="{% url 'accounts:trading_preferences' %}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-cog me-1"></i>Update Preferences
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Notifications</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Email Notifications</span>
                            {% if user.email_notifications %}
                                <span class="badge bg-success">On</span>
                            {% else %}
                                <span class="badge bg-secondary">Off</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>SMS Notifications</span>
                            {% if user.sms_notifications %}
                                <span class="badge bg-success">On</span>
                            {% else %}
                                <span class="badge bg-secondary">Off</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Newsletter</span>
                            {% if user.newsletter_subscription %}
                                <span class="badge bg-success">Subscribed</span>
                            {% else %}
                                <span class="badge bg-secondary">Unsubscribed</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Email Verified</span>
                            {% if user.email_verified %}
                                <span class="badge bg-success">Yes</span>
                            {% else %}
                                <span class="badge bg-warning">Pending</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="d-grid">
                        <a href="{% url 'accounts:trading_preferences' %}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-cog me-1"></i>Manage Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
        </div> <!-- End Main Content -->
    </div> <!-- End Row -->
</div> <!-- End Container -->
{% endblock %}
